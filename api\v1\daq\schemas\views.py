from fastapi import Depends, Request

from api.v1.utils import insert_log
from models.system.utils import LogDetailType, LogType
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    UserDataOption,
    TenantDataOption,
)
from .schema import SchemaCreateDTO, SchemaDTO
from models.daq import SchemasModel
from controllers.user import user_controller


router = TortoiseCRUDRouter(
    schema=SchemaDTO,
    create_schema=SchemaCreateDTO,
    db_model=SchemasModel,
    prefix="schemas",
    tags=["编排"],
    kbatch_create_route=False,
    user_data_option=UserDataOption.ALL_ONLY,
    tenant_data_option=TenantDataOption.ALL_ONLY,
)

