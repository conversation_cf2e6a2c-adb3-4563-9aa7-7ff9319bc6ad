"""
Descripttion:
version: 0.x
Author: zhai
Date: 2025-01-16 20:06:15
LastEditors: zhai
LastEditTime: 2025-01-16 23:19:00
"""

from pydantic import BaseModel, Config<PERSON><PERSON>, <PERSON><PERSON>
from datetime import datetime, date
from typing import List, Optional
from api.v1.fishery.firmware.schema import Ref_FirmwareDTO
from fastapi_crud_tortoise import MODEL_ID_TYPE, <PERSON><PERSON>ield
from enum import Enum


class UpdateResult(str, Enum):
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    IN_PROGRESS = "IN_PROGRESS"


class DeviceUpdateDTO(BaseModel):
    id: MODEL_ID_TYPE
    sn: Optional[str] = None
    name: Optional[str] = None
    version: Optional[str] = None
    result: Optional[UpdateResult] = UpdateResult.IN_PROGRESS


class UpdaterCreateDTO(BaseModel):
    name: str
    firmware_id: MODEL_ID_TYPE
    type: str
    model: str
    devices: List[DeviceUpdateDTO]
    notes: str


class UpdaterDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    time: Optional[datetime] = None
    firmware_id: Optional[MODEL_ID_TYPE] = None
    firmware: Optional[Ref_FirmwareDTO] = None
    type: Optional[str] = None
    model: Optional[str] = None
    devices: Optional[List[DeviceUpdateDTO]] = None
    result: Optional[str] = None
    progress: Optional[str] = None
    notes: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)
