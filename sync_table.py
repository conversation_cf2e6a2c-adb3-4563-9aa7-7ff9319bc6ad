'''
Descripttion: 
version: 0.x
Author: zhai
Date: 2024-07-01 20:32:45
LastEditors: zhai
LastEditTime: 2025-04-28 22:07:15
'''
import aiosqlite


async def copy_table_data(tb: str):
    async with aiosqlite.connect("app_system0.sqlite3") as source_db:
        async with source_db.execute(f"SELECT * FROM {tb};") as cursor:
            rows = await cursor.fetchall()
            columns = [description[0] for description in cursor.description]

    async with aiosqlite.connect("app_system.sqlite3") as target_db:
        await target_db.execute(
            f"DELETE FROM {tb};"
        )  # Ensure target table is empty
        if rows:
            # Quote column names to avoid conflicts with reserved keywords
            quoted_columns = [f'"{col}"' for col in columns]
            placeholders = ", ".join(["?"] * len(columns))
            insert_sql = f"INSERT INTO {tb} ({', '.join(quoted_columns)}) VALUES ({placeholders})"
            await target_db.executemany(insert_sql, rows)
        await target_db.commit()

async def main():
    await copy_table_data("users")
    await copy_table_data("menus")
    await copy_table_data("roles")
    await copy_table_data("users_roles")
    await copy_table_data("roles_menus")
    await copy_table_data("roles_apis")
    # await copy_table_data("mfst")
    # await copy_table_data("mfs")
    # await copy_table_data("equipment")
    # await copy_table_data("scheduler_task")
    # await copy_table_data("scheduler_task_record")
    # await copy_table_data("iotgroup")
    # await copy_table_data("iotpoint")
    # # await copy_table_data("iotdata")

    # await copy_table_data("api_logs")
    # await copy_table_data("dict_cate")
    # await copy_table_data("dict_item")
    # await copy_table_data("operations")

    # await copy_table_data("customer")
    # await copy_table_data("device")
    # await copy_table_data("fault_log")
    # await copy_table_data("feeding_log")
    # await copy_table_data("firmware")
    # await copy_table_data("operator")
    # await copy_table_data("organization")
    # await copy_table_data("tags")
    # await copy_table_data("tenant")
    # await copy_table_data("updater")


import asyncio

asyncio.run(main())
