'''
Descripttion: 
version: 0.x
Author: zhai
Date: 2023-06-19 08:44:13
LastEditors: zhai
LastEditTime: 2023-08-30 09:32:33
'''
# import streamlit.components.v1 as components
import json
from typing import List, Dict, Union

# import barfi components
from .block_builder import Block
from .compute_engine import ComputeEngine
from .manage_schema import load_schema_name, load_schemas, save_schema
from .manage_schema import editor_preset


# Declare a Streamlit component. `declare_component` returns a function
# that is used to create instances of the component. We're naming this
# function "_component_func", with an underscore prefix, because we don't want
# to expose it directly to users. Instead, we will create a custom wrapper
# function, below, that will serve as our component's public API.

# It's worth noting that this call to `declare_component` is the
# *only thing* you need to do to create the binding between Streamlit and
# your component frontend. Everything else we do in this file is simply a
# best practice.

# if not _RELEASE:
#     _component_func = components.declare_component(
#         "st_barfi",
#         url="http://localhost:3001",
#     )
# else:
#     parent_dir = os.path.dirname(os.path.abspath(__file__))
#     build_dir = os.path.join(parent_dir, "client")
#     _component_func = components.declare_component(
#         "st_barfi", path=build_dir)

# Create a wrapper function for the component. This is an optional
# best practice - we could simply expose the component function returned by
# `declare_component` and call it done. The wrapper allows us to customize
# our component's API: we can pre-process its input args, post-process its
# output value, and add a docstring for users.

# We use the special "key" argument to assign a fixed identity to this
# component instance. By default, when a component's arguments change,
# it is considered a new instance and will be re-mounted on the frontend
# and lose its current state. In this case, we want to vary the component's
# "name" argument without having it get recreated.


class NodeEngine():
    def __init__(self, base_blocks: Union[List[Block], Dict]):
        self.ce = None
        if isinstance(base_blocks, List):
            self.base_blocks_data = [block._export() for block in base_blocks]
            self.base_blocks_list = base_blocks
        elif isinstance(base_blocks, Dict):
            self.base_blocks_data = []
            self.base_blocks_list = []
            for category, block_list in base_blocks.items():
                if isinstance(block_list, List):
                    for block in block_list:
                        self.base_blocks_list.append(block)
                        block_data = block._export()
                        block_data['category'] = category
                        self.base_blocks_data.append(block_data)
                else:
                    raise TypeError(
                        'Invalid type for base_blocks passed to the st_barfi component.')
        else:
            raise TypeError(
                'Invalid type for base_blocks passed to the st_barfi component.')
        

    def base_blocks(self):
        return self.base_blocks_data

    def load_schema_name(self, load_schema: str = None):
        editor_schema = load_schema_name(load_schema)
        return editor_schema
    
    def schema_names(self):
        schemas_in_db = load_schemas()
        schema_names_in_db = schemas_in_db['schema_names']
        return schema_names_in_db

    def save_schema(self, schema_name: str, schema_data: Dict):
        save_schema(schema_name, schema_data)
    
    def execute_engine(self, schemas)->ComputeEngine:
        ce = ComputeEngine(blocks=self.base_blocks_list)
        ce.set_editor_state(schemas)
        ce._map_block_link()
        ce.execute()
        return ce

    def init_engine(self, schemas)->ComputeEngine:
        self.ce = ComputeEngine(blocks=self.base_blocks_list)
        self.ce.set_editor_state(schemas)
        self.ce._map_block_link()
        return self.ce
    
    def execute(self)->ComputeEngine:
        if self.ce:
            self.ce.execute()
        return self.ce
