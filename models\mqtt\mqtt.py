from tortoise import fields, models
from datetime import datetime
from fastapi_crud_tortoise import TenantCrudModel


class MqttMessageModel(TenantCrudModel):
    session_id = fields.CharField(max_length=255, null=True, default=None, description="唯一会话ID")
    sender = fields.CharField(max_length=20, description="发送者: cloud 或 device")
    type = fields.CharField(max_length=20, description="业务类型")
    system = fields.BooleanField(default=False, description="是否系统触发")
    operator = fields.CharField(max_length=255, description="操作人")
    sn = fields.CharField(max_length=255, null=True, default=None, description="设备SN")
    topic = fields.CharField(max_length=255, description="MQTT主题")
    payload = fields.TextField(description="发送的消息内容")
    status = fields.CharField(
        max_length=20, default="pending", description="状态: none, pending, replied, timeout"
    )

    class Meta:
        table = "mqtt_message"


class MqttReplyModel(TenantCrudModel):
    message = fields.ForeignKeyField("app_system.MqttMessageModel", related_name="replies")
    sender = fields.CharField(max_length=20, description="发送者: cloud 或 device")
    type = fields.CharField(max_length=20, description="业务类型")
    sn = fields.CharField(max_length=255, null=True, default=None, description="设备SN")
    topic = fields.CharField(max_length=255, description="MQTT主题")
    payload = fields.TextField(description="发送的消息内容")

    class Meta:
        table = "mqtt_reply"
