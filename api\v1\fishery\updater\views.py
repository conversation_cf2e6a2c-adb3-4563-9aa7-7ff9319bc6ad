from fastapi import Depends, Request

from api.v1.utils import insert_log
from models.system.utils import LogDetailType, LogType
from schemas.users import UserCreate, UserUpdate
from utils.security import get_password_hash
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    UserDataOption,
    TenantDataOption,
)
from .schema import UpdaterCreateDTO, UpdaterDTO
from models.fishery import UpdaterModel
from controllers.user import user_controller


router = TortoiseCRUDRouter(
    schema=UpdaterDTO,
    create_schema=UpdaterCreateDTO,
    db_model=UpdaterModel,
    prefix="updater",
    tags=["远程更新"],
    # kcreate_route=False,
    kbatch_create_route=False,
    kupsert_route=False,
    kupload_file_route=False,
    user_data_option=UserDataOption.ALL_ONLY,
    tenant_data_option=TenantDataOption.ALL_ONLY,
)
