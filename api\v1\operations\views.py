import json
from fastapi import Depends, Request

from api.v1.utils import insert_log
from models.system.utils import LogDetailType, LogType
from schemas.users import UserCreate, UserUpdate
from utils.security import get_password_hash
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    OperationsModel,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    UserDataOption,
    TenantDataOption,
)

from fastapi_crud_tortoise import insert_operation
from .schema import OperationsCreateDTO, OperationsDTO
from controllers.user import user_controller

router = TortoiseCRUDRouter(
    schema=OperationsDTO,
    create_schema=OperationsCreateDTO,
    db_model=OperationsModel,
    prefix="operations",
    tags=["操作日志"],
    kcreate_route=True,
    kbatch_create_route=False,
    kdelete_route=False,
    kbatch_delete_route=False,
    kdelete_all_route=False,
    kupdate_route=False,
    kget_by_id_route=False,
    kget_one_by_filter_route=False,
    klist_route=True,
    kquery_route=False,
    kquery_ex_route=True,
    kupsert_route=False,
    user_data_option=UserDataOption.ALL_ONLY,
    tenant_data_option=TenantDataOption.TENANT_DEFAULT,
)


# Overriding
@router.post("/create", response_model=RespModelT[OperationsDTO])
async def _(model: OperationsCreateDTO, request: Request):
    # 记录操作日志
    obj = await insert_operation(
        user=getattr(request.state, "user_name", "unknown"),
        action=model.action,
        target=model.target,
        notes=model.notes,
        tenant_id=getattr(request.state, "tenant_id", None),
        user_id=getattr(request.state, "user_id", None),
        trace_id=getattr(request.state, "x_request_id", None),
    )

    return resp_success(convert_to_pydantic(obj, OperationsDTO))
