VERSION="1.0.0"
APP_TITLE="FisheryCloud"
APP_DESCRIPTION="fishery cloud system"
DEBUG=true
SECRET_KEY="015a42020f023ac2c3eda3d45fe5ca3fef8921ce63589f6d4fcdef9814cd7fa7"

CORS_ORIGINS=["*"]
CORS_ALLOW_CREDENTIALS=True
CORS_ALLOW_METHODS=["*"]
CORS_ALLOW_HEADERS=["*"]

ADD_LOG_ORIGINS_INCLUDE=["*"]
ADD_LOG_ORIGINS_DECLUDE='[ "/system-manage", "/redoc", "/doc", "/openapi.json" ]'

# uvicorn 配置
HOST_PORT=9996
WORKERS=1

# SQLite 配置
# DB_ENGINE=sqlite
# DB_FILE=app_system.sqlite3

# PostgreSQL 配置

# DB_HOST=127.0.0.1
# DB_HOST=************
DB_HOST=pgsql

DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=12345678
DB_NAME=fishery
# DB_NAME=ifd

# MySQL 配置
# DB_ENGINE=mysql
# DB_HOST=localhost
# DB_PORT=3306
# DB_USER=root
# DB_PASSWORD=1234
# DB_NAME=ifd

# Redis 配置
# REDIS_HOST=127.0.0.1
# DB_HOST=************
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_BASE=2

MQTT_TOPIC_HEADER=BHTSJ

# 远程调用队列
FAST_API_CALL_QUEUE="redis_queue_fastapi_remote_call"
GATEWAY_MQTT_PUBLISH_QUEUE="redis_queue_gateway_mqtt_publish"