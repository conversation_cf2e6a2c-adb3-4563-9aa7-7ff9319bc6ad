from fastapi import Depends

from fastapi_crud_tortoise import Torto<PERSON><PERSON><PERSON><PERSON>outer, resp_success, TenantDataOption, UserDataOption
from api.v1.dict.schema import DictCateCreateDTO, DictCateDTO, DictItemCreateDTO, DictItemDTO
from api.v1.dummy.schema import DummyCreateD<PERSON>, DummyDTO
from models.dummy import DummyModel
from tortoise.contrib.pydantic import pydantic_model_creator

from api.v1.dummy.schema import (
    DepartmentCreateDTO,
    DepartmentDTO,
    EmployeeCreateDTO,
    EmployeeDTO,
    TeamCreateDTO,
    TeamDTO,
)
from models.dummy import DepartmentModel, EmployeeModel, TeamModel
from models.system.dictionary import DictCateModel, DictItemModel


dict_cate_router = TortoiseCRUDRouter(
    schema=DictCateDTO,
    create_schema=DictCateCreateDTO,
    db_model=DictCateModel,
    prefix="dict_cate",
    tags=["字典类别管理"],
    user_data_option=UserDataOption.ALL_ONLY,
    tenant_data_option=TenantDataOption.ALL_ONLY,
)

dict_item_router = TortoiseCRUDRouter(
    schema=DictItemDTO,
    create_schema=DictItemCreateDTO,
    db_model=DictItemModel,
    prefix="dict_item",
    tags=["字典项管理"],
    user_data_option=UserDataOption.ALL_ONLY,
    tenant_data_option=TenantDataOption.ALL_ONLY,
)
