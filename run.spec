# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import copy_metadata

datas = [('static/docs', 'static/docs')]
datas += copy_metadata('tortoise-orm')


a = Analysis(
    ['run.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=['fastapi_cache', 'passlib.handlers.argon2', 'tortoise.backends.sqlite', 'aerich.ddl.postgres', 'aerich.ddl.sqlite', 'tortoise.backends.mysql', 'aerich.ddl.mysql', 'cryptography', 'fastapi_crud_tortoise', 'tortoise.backends.asyncpg'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['pyinstaller'],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='run',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['main.ico'],
)
