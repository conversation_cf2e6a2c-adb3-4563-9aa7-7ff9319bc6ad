from fastapi import Depends, Request

from block.engine import get_json_blocks
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    UserDataOption,
    TenantDataOption,
)
from .schema import BlocksCreateDTO, BlocksDTO
from models.daq import BlocksModel
from controllers.user import user_controller


router = TortoiseCRUDRouter(
    schema=BlocksDTO,
    create_schema=BlocksCreateDTO,
    db_model=BlocksModel,
    prefix="blocks",
    tags=["脚本管理"],
    kbatch_create_route=False,
    kupsert_route=False,
    user_data_option=UserDataOption.ALL_ONLY,
    tenant_data_option=TenantDataOption.ALL_ONLY,
)


@router.get("/blocks/{id}", description="返回blocks")
async def get_blocks(request: Request, id: str):

    user_id = getattr(request.state, "user_id", None)
    blocks = await BlocksModel.filter(status=True, created_by=user_id).all()
    codes = [blocks.code for blocks in blocks]
    
    try:
        json_blocks = get_json_blocks(codes)
        return resp_success(data=json_blocks)
    except Exception as e:
        raise ValueError(f"Block Script Error:{e}")
