import jwt
from uuid import UUI<PERSON>
from passlib import pwd
from passlib.context import Crypt<PERSON>ontext

from schemas.login import JWTPayload
from settings import APP_SETTINGS
from json import JSONEncoder

# pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
pwd_context = CryptContext(schemes=["argon2"], deprecated="auto")


# ALGORITHM = "HS256"


# 自定义 JSON 编码器，处理 UUID 对象
class CustomJSONEncoder(JSONEncoder):
    def default(self, o):
        if isinstance(o, UUID):
            return str(o)  # 将 UUID 转换为字符串
        return super().default(o)


def create_access_token(*, data: JWTPayload):
    payload = data.model_dump().copy()
    encoded_jwt = jwt.encode(
        payload, APP_SETTINGS.SECRET_KEY, algorithm=APP_SETTINGS.JWT_ALGORITHM, json_encoder=CustomJSONEncoder
    )
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)


def generate_password() -> str:
    return pwd.genword()
