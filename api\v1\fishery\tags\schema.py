from pydantic import BaseModel, ConfigD<PERSON>, <PERSON><PERSON>
from datetime import datetime, date
from typing import List, Optional
from api.v1.fishery.device.schema import Ref_DeviceDTO
from fastapi_crud_tortoise import MODEL_ID_TYPE


class TagCreateDTO(BaseModel):
    name: str
    notes: str


class TagDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    notes: Optional[str] = None
    devices: Optional[List[Ref_DeviceDTO]] = None
    devices_refids: Optional[List[MODEL_ID_TYPE]] = None

    model_config = ConfigDict(from_attributes=True)
