from fastapi import Depends, Request

from api.v1.utils import insert_log
from models.system.utils import LogDetailType, LogType
from schemas.users import UserCreate, UserUpdate
from utils.security import get_password_hash
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    UserDataOption,
    TenantDataOption,
)
from .schema import FeedAliasCreateDTO, FeedAliasDTO
from models.fishery import FeedAliasModel
from controllers.user import user_controller


router = TortoiseCRUDRouter(
    schema=FeedAliasDTO,
    create_schema=FeedAliasCreateDTO,
    db_model=FeedAliasModel,
    prefix="feedalias",
    tags=["饲料别名管理"],
    kbatch_create_route=False,
    kupsert_route=False,
    user_data_option=UserDataOption.ALL_ONLY,
    tenant_data_option=TenantDataOption.TENANT_ONLY,
)
