[tool.poetry]
name = "fast-soy-iot"
version = "0.1.0"
description = ""
authors = ["kk <<EMAIL>>"]
readme = "README.md"
package-mode = false

[[tool.poetry.source]]
name = "aliyun-source"
url = "https://mirrors.aliyun.com/pypi/simple/"
priority = "supplemental"

[tool.poetry.dependencies]
python = ">=3.10,<3.14"
fastapi = "^0.115.8"    
uvicorn = "^0.29.0"
tortoise-orm = {extras = ["aiosqlite"], version = "0.21.4"}
pydantic = "2.7.1"
pydantic-settings = "^2.2.1"
passlib = "^1.7.4"
pyjwt = "^2.8.0"
loguru = "^0.7.2"
aerich = "^0.7.2"
email-validator = "^2.1.1"
setuptools = "^69.5.1"
argon2-cffi = "^23.1.0"
fastapi-cache2 = "^0.2.2"
redis = "^4.6.0"
orjson = "^3.10.12"
alibabacloud-dysmsapi20170525 = "^3.1.0"
uuid7 = "^0.1.0"
asyncpg = "^0.30.0"
python-multipart = "^0.0.20"
apscheduler = "^3.11.0"
sqlalchemy = "^2.0.38"
aiomysql = "^0.2.0"
pyinstaller = "^6.12.0"
openpyxl = "^3.1.5"
wh-fastapi-crud-tortoise = "^0.1.24"
numpy = "^2.2.4"
networkx = "^3.4.2"
barfi = "^1.1.0"
sse-starlette = "^2.3.3"
msgpack = "^1.1.0"
pretty-errors = "^1.2.25"
questdb = "^3.0.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
