from typing import cast
from urllib.parse import quote
from fastapi import Depends, Request
from fastapi.responses import StreamingResponse
from tortoise.expressions import RawSQL
from tortoise.functions import Sum, Count

from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    UserDataOption,
    TenantDataOption,
    PAGINATION,
    apply_user_tenant_filters,
    generate_excel,
    ExportConfig,
    UserDataFilter,
)

from .schema import (
    FeedingLogCreateDTO,
    FeedingLogDTO,
    FeedingLogQueryDTO,
    FeedingLogDetailDTO,
    FeedingStatTrendDTO,
    FeedingStatQueryDTO,
    FeedingStatTypeDTO,
    FeedingStatTypeTrendDTO,
    FeedingTypeDTO,
)
from models.fishery import FeedingLogModel


router = TortoiseCRUDRouter(
    schema=FeedingLogDTO,
    create_schema=FeedingLogCreateDTO,
    db_model=FeedingLogModel,
    prefix="feeding",
    tags=["饲喂记录"],
    kbatch_create_route=False,
    kupsert_route=False,
    user_data_option=UserDataOption.SELF_DEFAULT,
    tenant_data_option=TenantDataOption.TENANT_DEFAULT,
)


def __apply_user_filters(query, request, user_data_filter):
    if user_data_filter == UserDataFilter.SELF_DATA:
        user_id = getattr(request.state, "user_id", None)
        if user_id:
            return query.filter(device__binder_id=user_id)
    return query


@router.post("/query_detail", response_model=RespModelT[list[FeedingLogDetailDTO]])
async def get_stat_logs(
    request: Request,
    query: FeedingLogQueryDTO,
    pagination: PAGINATION = router.pagination,
    user_data_filter: router.user_data_filter_type = router.user_data_filter_defv,
    tenant_data_filter: router.tenant_data_filter_type = router.tenant_data_filter_defv,
):
    """获取饲喂记录列表（带设备信息）"""
    try:
        # 构建基础查询
        query_stmt = FeedingLogModel.all().select_related(
            "device", "device__tags", "device__organization", "device__organization__parent"
        )

        query_stmt = apply_user_tenant_filters(query_stmt, request, None, tenant_data_filter)
        query_stmt = __apply_user_filters(query_stmt, request, user_data_filter)

        # 添加查询条件
        if query.device_name:
            query_stmt = query_stmt.filter(device__name__icontains=query.device_name)
        if query.device_model:
            query_stmt = query_stmt.filter(device__model__icontains=query.device_model)
        if query.time_start:
            query_stmt = query_stmt.filter(time__gte=query.time_start)
        if query.time_end:
            query_stmt = query_stmt.filter(time__lte=query.time_end)
        if query.feed_mode:
            query_stmt = query_stmt.filter(feed_mode__icontains=query.feed_mode)
        if query.feed_type:
            query_stmt = query_stmt.filter(feed_type__icontains=query.feed_type)

        if user_data_filter == UserDataFilter.SELF_DATA:
            user_id = getattr(request.state, "user_id", None)
            if user_id:
                query_stmt = query_stmt.filter(device__binder_id=user_id)

        # 处理fishery和workshop查询条件
        # if query.fishery:
        #     # 查询所有匹配的第一层组织
        #     fishery_orgs = await OrganizationModel.filter(name__icontains=query.fishery, level=0).all()
        #     if fishery_orgs:
        #         # 获取所有子组织
        #         org_ids = []
        #         for org in fishery_orgs:
        #             sub_orgs = await OrganizationModel.filter(parent_id=org.id).all()
        #             org_ids.extend([sub_org.id for sub_org in sub_orgs])
        #         query_stmt = query_stmt.filter(device__organization_id__in=org_ids)

        # if query.workshop:
        #     # 查询所有匹配的第二层组织
        #     workshop_orgs = await OrganizationModel.filter(name__icontains=query.workshop, level=1).all()
        #     if workshop_orgs:
        #         org_ids = [org.id for org in workshop_orgs]
        #         query_stmt = query_stmt.filter(device__organization_id__in=org_ids)

        if query.organization_ids:
            query_stmt = query_stmt.filter(device__organization_id__in=query.organization_ids)

        # 处理tags查询条件
        if query.tags:
            # 使用PostgreSQL的JSON函数检查tags是否包含查询条件中的任意一个值
            query_stmt = query_stmt.filter(device__tags__name__in=query.tags)

        # 排序
        query_stmt = query_stmt.order_by("-time")

        # 分页查询
        skip, limit = pagination.get("skip"), pagination.get("limit")
        total = await query_stmt.count()

        if skip:
            query_stmt = query_stmt.offset(cast(int, skip))

        if limit:
            query_stmt = query_stmt.limit(limit)

        stat_logs = await query_stmt

        # 转换为DTO
        stat_log_dtos = []
        for log in stat_logs:
            dto = convert_to_pydantic(log, FeedingLogDetailDTO, relationships=False)
            # 设置workshop为设备关联的organization的名字
            if log.device.organization:
                dto["workshop"] = log.device.organization.name
            # 设置fishery为organization父节点的名字
            if log.device.organization and log.device.organization.parent:
                dto["fishery"] = log.device.organization.parent.name
            # 设置设备相关字段
            if log.device:
                dto["type"] = log.device.type
                dto["model"] = log.device.model
                # 使用记录的版本号
                # dto['version'] = log.device.version
            stat_log_dtos.append(dto)

        current = None
        size = None
        if skip and limit:
            size = limit
            current = skip // limit + 1

        return resp_success(
            stat_log_dtos,
            total,
            current,
            size,
        )

    except Exception as e:
        return resp_fail(f"查询失败: {str(e)}")


@router.post("/stat_type", name="料型统计", response_model=RespModelT[list[FeedingTypeDTO]])
async def stat_type(
    request: Request,
    query: FeedingStatQueryDTO,  # 复用现有查询条件
    user_data_filter: router.user_data_filter_type = router.user_data_filter_defv,
    tenant_data_filter: router.tenant_data_filter_type = router.tenant_data_filter_defv,
):
    """按组织和饲料类型统计下料量"""
    try:
        # 基础查询
        query_stmt = FeedingLogModel.all()

        query_stmt = apply_user_tenant_filters(query_stmt, request, None, tenant_data_filter)
        query_stmt = __apply_user_filters(query_stmt, request, user_data_filter)

        # 应用现有过滤条件
        if query.time_start:
            query_stmt = query_stmt.filter(time__gte=query.time_start)
        if query.time_end:
            query_stmt = query_stmt.filter(time__lte=query.time_end)
        if query.organization_ids:
            query_stmt = query_stmt.filter(device__organization_id__in=query.organization_ids)
        if query.device_id:
            query_stmt = query_stmt.filter(device_id=query.device_id)

        # 按组织和饲料类型分组统计
        stats = (
            await query_stmt.annotate(
                weight=Sum("weight"),
            )
            .group_by(
                "feed_type",
            )
            .values(
                "feed_type",
                "weight",
            )
        )

        stat_dtos = [
            {
                "type": item["feed_type"],
                "weight": item["weight"] or 0.0,
            }
            for item in stats
        ]

        return resp_success(stat_dtos)

    except Exception as e:
        return resp_fail(f"统计失败: {str(e)}")


@router.post("/stat_fishery_type", name="渔场、料型统计", response_model=RespModelT[list[FeedingStatTypeDTO]])
async def stat_fishery_type(
    request: Request,
    query: FeedingStatQueryDTO,  # 复用现有查询条件
    user_data_filter: router.user_data_filter_type = router.user_data_filter_defv,
    tenant_data_filter: router.tenant_data_filter_type = router.tenant_data_filter_defv,
):
    """按组织和饲料类型统计下料量"""
    try:
        # 基础查询
        query_stmt = FeedingLogModel.all().select_related(
            "device", "device__organization", "device__organization__parent"
        )

        query_stmt = apply_user_tenant_filters(query_stmt, request, None, tenant_data_filter)
        query_stmt = __apply_user_filters(query_stmt, request, user_data_filter)

        # 应用现有过滤条件
        if query.time_start:
            query_stmt = query_stmt.filter(time__gte=query.time_start)
        if query.time_end:
            query_stmt = query_stmt.filter(time__lte=query.time_end)
        if query.organization_ids:
            query_stmt = query_stmt.filter(device__organization_id__in=query.organization_ids)
        if query.device_id:
            query_stmt = query_stmt.filter(device_id=query.device_id)

        # 按组织和饲料类型分组统计
        stats = (
            await query_stmt.annotate(
                weight=Sum("weight"),
            )
            .group_by(
                "device__organization__parent_id",
                "device__organization__parent__name",
                "feed_type",
            )
            .values(
                "device__organization__parent_id",
                "device__organization__parent__name",
                "feed_type",
                "weight",
            )
        )

        # 转换为DTO
        stat_dtos_dict = {}
        for item in stats:
            org_id = item["device__organization__parent_id"]
            if org_id not in stat_dtos_dict:
                stat_dtos_dict[org_id] = {
                    "fishery": item["device__organization__parent__name"],
                    "feeding": [],
                }
            stat_dtos_dict[org_id]["feeding"].append(
                {"type": item["feed_type"], "weight": item["weight"] or 0.0}
            )

        return resp_success(list(stat_dtos_dict.values()))

    except Exception as e:
        return resp_fail(f"统计失败: {str(e)}")


@router.post(
    "/stat_fishery_trend", name="渔场、日期统计", response_model=RespModelT[list[FeedingStatTrendDTO]]
)
async def stat_fishery_trend(
    request: Request,
    query: FeedingStatQueryDTO,  # 复用现有查询条件
    user_data_filter: router.user_data_filter_type = router.user_data_filter_defv,
    tenant_data_filter: router.tenant_data_filter_type = router.tenant_data_filter_defv,
):
    """按天和渔场统计下料量"""
    try:
        # 基础查询
        query_stmt = FeedingLogModel.all().select_related(
            "device", "device__organization", "device__organization__parent"
        )

        query_stmt = apply_user_tenant_filters(query_stmt, request, None, tenant_data_filter)
        query_stmt = __apply_user_filters(query_stmt, request, user_data_filter)

        # 应用现有过滤条件
        if query.time_start:
            query_stmt = query_stmt.filter(time__gte=query.time_start)
        if query.time_end:
            query_stmt = query_stmt.filter(time__lte=query.time_end)
        if query.organization_ids:
            query_stmt = query_stmt.filter(device__organization_id__in=query.organization_ids)
        if query.device_id:
            query_stmt = query_stmt.filter(device_id=query.device_id)

        # 按天和设备分组统计
        stats = (
            await query_stmt.annotate(
                day=RawSQL("time::DATE"),  # 使用PostgreSQL的::DATE语法
                weight=Sum("weight"),
            )
            .group_by(
                "device__organization__parent_id",
                "device__organization__parent__name",
                "day",
            )
            .values(
                "device__organization__parent_id",
                "device__organization__parent__name",
                "day",
                "weight",
            )
        )

        # stat_dtos = []
        # for item in stats:
        #     dto = convert_to_pydantic(item, FeedingLogDetailDTO, relationships=False)
        #     # 设置workshop为设备关联的organization的名字
        #     if item.device.organization:
        #         dto["workshop"] = item.device.organization.name
        #     # 设置fishery为organization父节点的名字
        #     if item.device.organization and item.device.organization.parent:
        #         dto["fishery"] = item.device.organization.parent.name

        #     stat_dtos.append(dto)

        # stat_dtos = [convert_to_pydantic(item, FeedingStatTrendDTO) for item in stats]

        stat_dtos = [
            {
                "fishery": item["device__organization__parent__name"],  # 使用别名
                "day": str(item["day"]),  # 将日期转换为字符串
                "weight": item["weight"] or 0.0,
            }
            for item in stats
        ]

        return resp_success(stat_dtos)

    except Exception as e:
        return resp_fail(f"统计失败: {str(e)}")


@router.post(
    "/stat_workshop_trend", name="车间、日期统计", response_model=RespModelT[list[FeedingStatTrendDTO]]
)
async def stat_workshop_trend(
    request: Request,
    query: FeedingStatQueryDTO,  # 复用现有查询条件
    user_data_filter: router.user_data_filter_type = router.user_data_filter_defv,
    tenant_data_filter: router.tenant_data_filter_type = router.tenant_data_filter_defv,
):
    """按天和车间统计下料量"""
    try:
        # 基础查询
        query_stmt = FeedingLogModel.all().select_related(
            "device", "device__organization", "device__organization__parent"
        )

        query_stmt = apply_user_tenant_filters(query_stmt, request, None, tenant_data_filter)
        query_stmt = __apply_user_filters(query_stmt, request, user_data_filter)

        # 应用现有过滤条件
        if query.time_start:
            query_stmt = query_stmt.filter(time__gte=query.time_start)
        if query.time_end:
            query_stmt = query_stmt.filter(time__lte=query.time_end)
        if query.organization_ids:
            query_stmt = query_stmt.filter(device__organization_id__in=query.organization_ids)
        if query.device_id:
            query_stmt = query_stmt.filter(device_id=query.device_id)

        # 按天和设备分组统计
        stats = (
            await query_stmt.annotate(
                day=RawSQL("time::DATE"),  # 使用PostgreSQL的::DATE语法
                weight=Sum("weight"),
            )
            .group_by(
                "device__organization_id",
                "day",
                "device__organization__name",
                "device__organization__parent__name",
            )
            .values(
                "device__organization_id",
                "day",
                "weight",
                "device__organization__name",
                "device__organization__parent__name",
            )
        )

        # stat_dtos = []
        # for item in stats:
        #     dto = convert_to_pydantic(item, FeedingLogDetailDTO, relationships=False)
        #     # 设置workshop为设备关联的organization的名字
        #     if item.device.organization:
        #         dto["workshop"] = item.device.organization.name
        #     # 设置fishery为organization父节点的名字
        #     if item.device.organization and item.device.organization.parent:
        #         dto["fishery"] = item.device.organization.parent.name

        #     stat_dtos.append(dto)

        # stat_dtos = [convert_to_pydantic(item, FeedingStatTrendDTO) for item in stats]

        stat_dtos = [
            {
                "fishery": item["device__organization__parent__name"],  # 使用别名
                "workshop": item["device__organization__name"],  # 使用别名
                "day": str(item["day"]),  # 将日期转换为字符串
                "weight": item["weight"] or 0.0,
            }
            for item in stats
        ]

        return resp_success(stat_dtos)

    except Exception as e:
        return resp_fail(f"统计失败: {str(e)}")


@router.post(
    "/stat_workshop_type", name="车间、料型统计", response_model=RespModelT[list[FeedingStatTypeDTO]]
)
async def stat_workshop_type(
    request: Request,
    query: FeedingStatQueryDTO,  # 复用现有查询条件
    user_data_filter: router.user_data_filter_type = router.user_data_filter_defv,
    tenant_data_filter: router.tenant_data_filter_type = router.tenant_data_filter_defv,
):
    """按组织和饲料类型统计下料量"""
    try:
        # 基础查询
        query_stmt = FeedingLogModel.all().select_related(
            "device", "device__organization", "device__organization__parent"
        )

        query_stmt = apply_user_tenant_filters(query_stmt, request, None, tenant_data_filter)
        query_stmt = __apply_user_filters(query_stmt, request, user_data_filter)

        # 应用现有过滤条件
        if query.time_start:
            query_stmt = query_stmt.filter(time__gte=query.time_start)
        if query.time_end:
            query_stmt = query_stmt.filter(time__lte=query.time_end)
        if query.organization_ids:
            query_stmt = query_stmt.filter(device__organization_id__in=query.organization_ids)
        if query.device_id:
            query_stmt = query_stmt.filter(device_id=query.device_id)

        # 按组织和饲料类型分组统计
        stats = (
            await query_stmt.annotate(
                weight=Sum("weight"),
            )
            .group_by(
                "device__organization_id",
                "feed_type",
                "device__organization__name",
                "device__organization__parent__name",
            )
            .values(
                "device__organization_id",
                "feed_type",
                "weight",
                "device__organization__name",
                "device__organization__parent__name",
            )
        )

        # 转换为DTO
        stat_dtos_dict = {}
        for item in stats:
            org_id = item["device__organization_id"]
            if org_id not in stat_dtos_dict:
                stat_dtos_dict[org_id] = {
                    "fishery": item["device__organization__parent__name"],
                    "workshop": item["device__organization__name"],
                    "feeding": [],
                }
            stat_dtos_dict[org_id]["feeding"].append(
                {"type": item["feed_type"], "weight": item["weight"] or 0.0}
            )

        return resp_success(convert_to_pydantic(list(stat_dtos_dict.values()), FeedingStatTypeDTO))

    except Exception as e:
        return resp_fail(f"统计失败: {str(e)}")


@router.post(
    "/stat_workshop_type_trend",
    name="日期、车间、料型统计",
    response_model=RespModelT[list[FeedingStatTypeTrendDTO]],
)
async def stat_workshop_type_trend(
    request: Request,
    query: FeedingStatQueryDTO,  # 复用现有查询条件
    # pagination: PAGINATION = router.pagination,
    user_data_filter: router.user_data_filter_type = router.user_data_filter_defv,
    tenant_data_filter: router.tenant_data_filter_type = router.tenant_data_filter_defv,
):
    """日期、车间、料型统计"""
    try:
        # 基础查询
        query_stmt = FeedingLogModel.all().select_related(
            "device", "device__organization", "device__organization__parent"
        )

        query_stmt = apply_user_tenant_filters(query_stmt, request, None, tenant_data_filter)
        query_stmt = __apply_user_filters(query_stmt, request, user_data_filter)

        # 应用现有过滤条件
        if query.time_start:
            query_stmt = query_stmt.filter(time__gte=query.time_start)
        if query.time_end:
            query_stmt = query_stmt.filter(time__lte=query.time_end)
        if query.organization_ids:
            query_stmt = query_stmt.filter(device__organization_id__in=query.organization_ids)
        if query.device_id:
            query_stmt = query_stmt.filter(device_id=query.device_id)

        # 按天和设备分组统计
        query_stmt = (
            query_stmt.annotate(
                day=RawSQL("time::DATE"),  # 使用PostgreSQL的::DATE语法
                weight=Sum("weight"),
            )
            .group_by(
                "day",
                "device__organization_id",
                "feed_type",
                "device__organization__name",
                "device__organization__parent__name",
            )
            .values(
                "day",
                "device__organization_id",
                "feed_type",
                "weight",
                "device__organization__name",
                "device__organization__parent__name",
            )
        )

        # #######################################################################
        # # 使用 Count 单字段 distinct 统计 √
        # query_groups = FeedingLogModel.annotate(count=Count(RawSQL("time::DATE"), distinct=True)).values(
        #     "count"
        # )

        # total_groups = await query_groups
        # print(f"Total unique groups: {total_groups[0]['total']}")

        # #########################################################################################
        # # 使用RawSQL 多字段 distinct 统计 √
        # query_groups = FeedingLogModel.annotate(
        #     total=RawSQL("COUNT(DISTINCT (time::DATE, feed_type))")
        # ).values("total")

        # total_groups = await query_groups
        # print(f"Total unique groups: {total_groups[0]['total']}")

        # #########################################################################################
        # # 统计分组数量，但返回的是原始记录的数量
        # query_groups = (
        #     FeedingLogModel.all()
        #     .select_related("device", "device__organization")
        #     .annotate(
        #         day=RawSQL("time::DATE"),  # 使用 PostgreSQL 的 ::DATE 语法
        #     )
        #     .group_by(
        #         "day",
        #         "device__organization_id",
        #         "feed_type",
        #     )
        #     .count()
        # )
        # total_groups = await query_groups
        # print(f"Total unique groups: {total_groups}")

        # #########################################################################################
        # # 返回多个字段的不重复组合，但是没有统计，需要len(groups)
        # query_groups = (
        #     FeedingLogModel.all()
        #     .select_related("device", "device__organization")
        #     .annotate(
        #         day=RawSQL("time::DATE"),  # 使用 PostgreSQL 的 ::DATE 语法
        #     )
        #     .distinct()
        #     .values(
        #         "day",
        #         "device__organization_id",
        #         "feed_type",
        #     )
        # )
        # total_groups = await query_groups
        # total_groups = len(total_groups)

        #########################################################################################
        # count_query = """
        #     SELECT
        #         COUNT(*)
        #     FROM (
        #         SELECT
        #             time::DATE AS day,
        #             device.organization_id AS device__organization_id,
        #             feeding_log.feed_type
        #         FROM
        #             feeding_log
        #         JOIN
        #             device ON feeding_log.device_id = device.id
        #         JOIN
        #             organization ON device.organization_id = organization.id
        #         GROUP BY
        #             day,
        #             device.organization_id,
        #             feed_type
        #     );
        # """

        # # 执行原生 SQL 查询
        # connection = Tortoise.get_connection("pgsql")
        # result = await connection.execute_query_dict(count_query)
        # total = result[0]["count"]
        #########################################################################################

        # skip, limit = pagination.get("skip"), pagination.get("limit")
        # if skip:
        #     query_stmt = query_stmt.offset(cast(int, skip))
        # if limit:
        #     query_stmt = query_stmt.limit(limit)

        stats = await query_stmt

        # stat_dtos = []
        # for item in stats:
        #     dto = convert_to_pydantic(item, FeedingLogDetailDTO, relationships=False)
        #     # 设置workshop为设备关联的organization的名字
        #     if item.device.organization:
        #         dto["workshop"] = item.device.organization.name
        #     # 设置fishery为organization父节点的名字
        #     if item.device.organization and item.device.organization.parent:
        #         dto["fishery"] = item.device.organization.parent.name

        #     stat_dtos.append(dto)

        # stat_dtos = [convert_to_pydantic(item, FeedingStatTrendDTO) for item in stats]

        stat_dtos = [
            {
                "day": str(item["day"]),  # 将日期转换为字符串
                "workshop": item["device__organization__parent__name"],  # 使用别名
                "fishery": item["device__organization__name"],  # 使用别名
                "feed_type": item["feed_type"],
                "weight": item["weight"] or 0.0,
            }
            for item in stats
        ]

        return resp_success(stat_dtos)

        # current = None
        # size = None
        # if skip and limit:
        #     size = limit
        #     current = skip // limit + 1

        # return resp_success(stat_dtos, total, current, size)

    except Exception as e:
        return resp_fail(f"统计失败: {str(e)}")


@router.post("/export_detail", response_model=RespModelT[list[FeedingLogDetailDTO]])
async def _(
    request: Request,
    config: ExportConfig[FeedingLogQueryDTO],
    pagination: PAGINATION = router.pagination,
    user_data_filter: router.user_data_filter_type = router.user_data_filter_defv,
    tenant_data_filter: router.tenant_data_filter_type = router.tenant_data_filter_defv,
):
    """获取饲喂记录列表（带设备信息）"""
    try:
        # 构建基础查询
        query_stmt = FeedingLogModel.all().select_related(
            "device", "device__tags", "device__organization", "device__organization__parent"
        )

        query_stmt = apply_user_tenant_filters(query_stmt, request, None, tenant_data_filter)
        query_stmt = __apply_user_filters(query_stmt, request, user_data_filter)

        query = config.filter
        # 添加查询条件
        if query.device_name:
            query_stmt = query_stmt.filter(device__name__icontains=query.device_name)
        if query.device_model:
            query_stmt = query_stmt.filter(device__model__icontains=query.device_model)
        if query.time_start:
            query_stmt = query_stmt.filter(time__gte=query.time_start)
        if query.time_end:
            query_stmt = query_stmt.filter(time__lte=query.time_end)
        if query.feed_mode:
            query_stmt = query_stmt.filter(feed_mode__icontains=query.feed_mode)
        if query.feed_type:
            query_stmt = query_stmt.filter(feed_type__icontains=query.feed_type)

        if query.organization_ids:
            query_stmt = query_stmt.filter(device__organization_id__in=query.organization_ids)

        # 处理tags查询条件
        if query.tags:
            # 使用PostgreSQL的JSON函数检查tags是否包含查询条件中的任意一个值
            query_stmt = query_stmt.filter(device__tags__name__in=query.tags)

        # 排序
        query_stmt = query_stmt.order_by("-time")

        # 分页查询
        skip, limit = pagination.get("skip"), pagination.get("limit")
        total = await query_stmt.count()

        if skip:
            query_stmt = query_stmt.offset(cast(int, skip))

        if limit:
            query_stmt = query_stmt.limit(limit)

        stat_logs = await query_stmt

        # 转换为DTO
        stat_log_dtos = []
        for log in stat_logs:
            dto = convert_to_pydantic(log, FeedingLogDetailDTO, relationships=False)
            # 设置workshop为设备关联的organization的名字
            if log.device.organization:
                dto["workshop"] = log.device.organization.name
            # 设置fishery为organization父节点的名字
            if log.device.organization and log.device.organization.parent:
                dto["fishery"] = log.device.organization.parent.name
            # 设置设备相关字段
            if log.device:
                dto["type"] = log.device.type
                dto["model"] = log.device.model
                # 使用记录的版本号
                # dto['version'] = log.device.version
            stat_log_dtos.append(dto)

        # 生成 Excel
        excel_stream = generate_excel(stat_log_dtos, config)

        # Encode the filename using urllib.parse.quote
        encoded_filename = quote(config.filename)

        # 构建响应
        return StreamingResponse(
            excel_stream,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename={encoded_filename}",
                "Access-Control-Expose-Headers": "Content-Disposition",
            },
        )

    except Exception as e:
        print(f"导出失败: {str(e)}")
        return resp_fail(msg=f"文件生成失败: {str(e)}")


@router.post(
    "/export_stat_workshop_type_trend",
    name="日期、车间、料型统计",
    response_model=RespModelT[list[FeedingStatTypeTrendDTO]],
)
async def _(
    request: Request,
    config: ExportConfig[FeedingStatQueryDTO],
    # pagination: PAGINATION = router.pagination,
    user_data_filter: router.user_data_filter_type = router.user_data_filter_defv,
    tenant_data_filter: router.tenant_data_filter_type = router.tenant_data_filter_defv,
):
    """日期、车间、料型统计"""
    try:
        # 基础查询
        query_stmt = FeedingLogModel.all().select_related(
            "device", "device__organization", "device__organization__parent"
        )

        query_stmt = apply_user_tenant_filters(query_stmt, request, None, tenant_data_filter)
        query_stmt = __apply_user_filters(query_stmt, request, user_data_filter)

        # 应用现有过滤条件
        query = config.filter
        if query.time_start:
            query_stmt = query_stmt.filter(time__gte=query.time_start)
        if query.time_end:
            query_stmt = query_stmt.filter(time__lte=query.time_end)
        if query.organization_ids:
            query_stmt = query_stmt.filter(device__organization_id__in=query.organization_ids)
        if query.device_id:
            query_stmt = query_stmt.filter(device_id=query.device_id)

        # 按天和设备分组统计
        query_stmt = (
            query_stmt.annotate(
                day=RawSQL("time::DATE"),  # 使用PostgreSQL的::DATE语法
                weight=Sum("weight"),
            )
            .group_by(
                "day",
                "device__organization_id",
                "feed_type",
                "device__organization__name",
                "device__organization__parent__name",
            )
            .values(
                "day",
                "device__organization_id",
                "feed_type",
                "weight",
                "device__organization__name",
                "device__organization__parent__name",
            )
        )

        stats = await query_stmt

        stat_dtos = [
            {
                "day": str(item["day"]),  # 将日期转换为字符串
                "workshop": item["device__organization__parent__name"],  # 使用别名
                "fishery": item["device__organization__name"],  # 使用别名
                "feed_type": item["feed_type"],
                "weight": item["weight"] or 0.0,
            }
            for item in stats
        ]

        # 生成 Excel
        excel_stream = generate_excel(stat_dtos, config)

        # Encode the filename using urllib.parse.quote
        encoded_filename = quote(config.filename)

        # 构建响应
        return StreamingResponse(
            excel_stream,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename={encoded_filename}",
                "Access-Control-Expose-Headers": "Content-Disposition",
            },
        )

    except Exception as e:
        print(f"导出失败: {str(e)}")
        return resp_fail(msg=f"文件生成失败: {str(e)}")
