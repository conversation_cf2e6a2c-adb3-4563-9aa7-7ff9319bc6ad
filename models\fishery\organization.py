from datetime import datetime
from tortoise import Model, fields
from fastapi_crud_tortoise import TenantCrudModel


class OrganizationModel(TenantCrudModel):
    """组织模型（树形结构）"""

    # 组织名称
    name = fields.CharField(max_length=255)
    # 父组织，root 组织的 parent 为 None
    parent = fields.ForeignKeyField("app_system.OrganizationModel", related_name="children", null=True)
    # 组织层级，根节点的层级为0，子节点的层级依此递增
    level = fields.IntField(default=0)
    area = fields.CharField(max_length=255, null=True)
    location = fields.CharField(max_length=255, null=True)
    status = fields.BooleanField(default=True)

    class Meta:
        table = "organization"

    async def save(self, *args, **kwargs):
        """重写 save 方法，更新层级"""
        if self.parent:
            await self.fetch_related("parent")  # 确保加载 parent 关联对象
            if self.parent:
                self.level = self.parent.level + 1

        return await super().save(*args, **kwargs)


__all__ = ["OrganizationModel"]
