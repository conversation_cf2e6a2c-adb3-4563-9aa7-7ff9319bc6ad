import os
import time
from fastapi import FastAPI, File, UploadFile
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import uvicorn
import subprocess
import asyncio

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有域名
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
)

# 配置静态文件目录，将 web 文件夹中的内容映射到 /static 路径
app.mount("/static", StaticFiles(directory="static"), name="static")


# 根路径直接返回 index.html
@app.get("/", response_class=HTMLResponse)
async def root():
    # 确定 index.html 文件的路径
    index_path = "index.html"

    # 读取文件内容并返回
    with open(index_path, "r", encoding="utf-8") as file:
        content = file.read()

    return HTMLResponse(content=content)


# 网卡列表
@app.get("/api/network_cards", response_model=List[str])
async def get_network_cards():
    try:
        # 使用 subprocess 运行 'ifconfig -a' 命令
        result = subprocess.run(["ifconfig", "-a"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

        # 检查命令是否成功执行
        if result.returncode != 0:
            print(f"Error: {result.stderr}")
            return []

        # 解析命令输出
        interfaces = []
        for line in result.stdout.splitlines():
            if line.startswith(" " * 8):
                continue

            if line.startswith("eth"):  # 匹配接口行
                interface_name = line.split(":")[0]
                interfaces.append(interface_name)

        return interfaces

    except Exception as e:
        print(f"An error occurred: {e}")
        return []


# 网卡配置
class IpConfig(BaseModel):
    autoip: bool
    card: Optional[str] = None
    ip: Optional[str] = None
    mask: Optional[str] = None
    gateway: Optional[str] = None


@app.post("/api/set_ip")
async def set_ip(data: IpConfig):
    # 获取当前工作目录
    current_directory = os.path.abspath(os.path.dirname(__file__))
    script_filename = "set_ip.sh"

    # 构建脚本的完整路径
    script_path = os.path.join(current_directory, script_filename)

    # <参数1（网口如eth0）> <参数2（ip如*************）> <参数3（dns如24）>  <参数4（网关如************）>
    if data.autoip:
        arg1 = "auto"
        arg2 = "auto"
        arg3 = "auto"
    else:
        arg1 = data.ip
        arg2 = data.mask
        arg3 = data.gateway

    try:
        result = subprocess.run(
            [script_path, data.card, arg1, arg2, arg3],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=True,
        )

        # 打印脚本的输出（如果有的话）
        print("Script output:", result.stdout)

        # 打印错误信息（如果有的话）
        print("Script error:", result.stderr)

        return {"success": True, "output": result.stdout.strip()}  # 返回成功信息和输出

    except subprocess.CalledProcessError as e:
        # 处理命令执行失败的情况
        print("Script failed with error:", e)
        print("Script error output:", e.stderr)
        return {"success": False, "error": e.stderr.strip()}  # 返回错误信息


# 网卡配置
class ServerConfig(BaseModel):
    on: bool
    ip: Optional[str] = None


@app.post("/api/set_server")
async def set_server(data: ServerConfig):
    # 获取当前工作目录
    current_directory = os.path.abspath(os.path.dirname(__file__))
    script_filename = "set_serverip.sh"

    # 构建脚本的完整路径
    script_path = os.path.join(current_directory, script_filename)

    # <参数1（本地ip以及端口如“*************:44450”或者“*************” 不输入端口默认80）> <参数2（是否启用本地端true/false）>
    arg1 = data.ip
    arg2 = "true" if data.on else "false"

    try:
        result = subprocess.run(
            [script_path, arg1, arg2],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=True,
        )

        # 打印脚本的输出（如果有的话）
        print("Script output:", result.stdout)

        # 打印错误信息（如果有的话）
        print("Script error:", result.stderr)

        return {"success": True, "output": result.stdout.strip()}  # 返回成功信息和输出

    except subprocess.CalledProcessError as e:
        # 处理命令执行失败的情况
        print("Script failed with error:", e)
        print("Script error output:", e.stderr)
        return {"success": False, "error": e.stderr.strip()}  # 返回错误信息


# 固定升级文件名
update_filename = "updatefile.bin"


# 上传升级文件
@app.post("/api/upload_file")
async def upload_file(file: UploadFile = File(...)):
    FILE_ROOT = "uploads"
    os.makedirs(FILE_ROOT, exist_ok=True)

    file_location = f"{FILE_ROOT}/{update_filename}"

    with open(file_location, "wb") as f:
        f.write(await file.read())

    return {"filename": update_filename, "location": file_location}


# 执行升级
@app.get("/api/update")
async def execute_command():
    try:
        # 模拟升级
        await asyncio.sleep(10)

        # 使用 update_filename 升级
        return {"success": True, "msg": "ok"}
    except Exception as e:
        return {"success": False, "msg": "failed", "error": "执行失败"}


if __name__ == "__main__":
    try:
        uvicorn.run("main:app", host="0.0.0.0", port=8086, reload=False)
    except KeyboardInterrupt:
        ...
