from fastapi import Depends, Request

from api.v1.utils import insert_log
from models.system.utils import LogDetailType, LogType
from schemas.users import UserCreate, UserUpdate
from utils.security import get_password_hash
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    UserDataOption,
    TenantDataOption,
)
from .schema import TagCreateDTO, TagDTO
from models.fishery import TagModel
from controllers.user import user_controller


router = TortoiseCRUDRouter(
    schema=TagDTO,
    create_schema=TagCreateDTO,
    db_model=TagModel,
    prefix="tags",
    tags=["标签管理"],
    kbatch_create_route=False,
    kupsert_route=False,
    user_data_option=UserDataOption.ALL_ONLY,
    tenant_data_option=TenantDataOption.TENANT_ONLY,
)
