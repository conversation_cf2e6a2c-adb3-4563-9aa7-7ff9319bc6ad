from pydantic import BaseModel, Config<PERSON><PERSON>, <PERSON><PERSON>
from datetime import datetime, date
from typing import List, Optional
from api.v1.fishery.device.schema import Ref_DeviceDTO
from api.v1.fishery.organization.schema import Ref_OrganizationDTO
from schemas.users import Ref_UserDTO
from fastapi_crud_tortoise import MODEL_ID_TYPE


class OfflineLogCreateDTO(BaseModel):
    tenant_id: Optional[MODEL_ID_TYPE] = None
    device_id: MODEL_ID_TYPE
    offline_time: datetime
    online_time: Optional[datetime] = None
    status: Optional[bool] = True
    notes: Optional[str] = None


class OfflineLogDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    tenant_id: Optional[MODEL_ID_TYPE] = None
    device_id: Optional[MODEL_ID_TYPE] = None
    device: Optional[Ref_DeviceDTO] = None
    offline_time: Optional[datetime] = None
    online_time: Optional[datetime] = None
    status: Optional[bool] = None
    notes: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class OfflineLogQueryDTO(BaseModel):
    device_name: Optional[str] = None
    status: Optional[bool] = None
    offline_time_start: Optional[datetime] = None
    offline_time_end: Optional[datetime] = None
    online_time_start: Optional[datetime] = None
    online_time_end: Optional[datetime] = None
    # fishery: Optional[str] = None
    # workshop: Optional[str] = None
    organization_ids: Optional[List[MODEL_ID_TYPE]] = None
    type: Optional[str] = None
    model: Optional[str] = None
    version: Optional[str] = None


class OfflineLogDetailDTO(BaseModel):
    device_id: Optional[MODEL_ID_TYPE] = None
    device_name: Optional[str] = None
    status: Optional[bool] = None
    offline_time: Optional[datetime] = None
    online_time: Optional[datetime] = None
    fishery: Optional[str] = None
    workshop: Optional[str] = None
    type: Optional[str] = None
    model: Optional[str] = None
    version: Optional[str] = None
