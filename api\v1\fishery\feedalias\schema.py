from pydantic import BaseModel, ConfigD<PERSON>, <PERSON><PERSON>
from datetime import datetime, date
from typing import List, Optional
from api.v1.fishery.device.schema import Ref_DeviceDTO
from fastapi_crud_tortoise import MODEL_ID_TYPE


class FeedAliasCreateDTO(BaseModel):
    name: str
    alias: str


class FeedAliasDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    alias: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)
