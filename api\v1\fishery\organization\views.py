from fastapi import Depends, Request

from api.v1.utils import insert_log
from models.system.utils import LogDetailType, LogType
from schemas.users import UserCreate, UserUpdate
from utils.security import get_password_hash
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    UserDataOption,
    TenantDataOption,
)
from .schema import OrganizationCreateDTO, OrganizationDTO
from models.fishery import OrganizationModel
from controllers.user import user_controller


router = TortoiseCRUDRouter(
    schema=OrganizationDTO,
    create_schema=OrganizationCreateDTO,
    db_model=OrganizationModel,
    prefix="organization",
    tags=["组织管理"],
    kbatch_create_route=False,
    kupsert_route=False,
    user_data_option=UserDataOption.SELF_DEFAULT,
    tenant_data_option=TenantDataOption.TENANT_DEFAULT,
)
