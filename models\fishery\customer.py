"""
Descripttion: 
version: 0.x
Author: zhai
Date: 2024-05-25 09:03:01
LastEditors: zhai
LastEditTime: 2025-01-04 22:09:29
"""

from datetime import datetime
from tortoise import Model, fields
from fastapi_crud_tortoise import TenantCrudModel


class CustomerModel(TenantCrudModel):
    """客户表"""

    name = fields.CharField(max_length=255)
    contact_person = fields.CharField(max_length=255)
    contact_info = fields.CharField(max_length=20)
    location = fields.CharField(max_length=255)
    account = fields.CharField(max_length=20)  # 管理员账号是手机号
    password = fields.CharField(max_length=255)  # 密码会经过加密存储
    status = fields.BooleanField(default=True)  # 默认为True，表示客户可用

    class Meta:
        table = "customer"


class OperatorModel(TenantCrudModel):
    """作业账号表"""

    name = fields.<PERSON><PERSON><PERSON><PERSON>(max_length=255)
    account = fields.Char<PERSON><PERSON>(max_length=20)
    password = fields.Char<PERSON>ield(max_length=255)
    status = fields.BooleanField(default=True)

    class Meta:
        table = "operator"


class FeedAliasModel(TenantCrudModel):
    """饲料别名表"""

    name = fields.CharField(max_length=255)
    alias = fields.CharField(max_length=255)

    class Meta:
        table = "feed_alias"


__all__ = ["CustomerModel", "OperatorModel", "FeedAliasModel", "FeedAliasModel"]
