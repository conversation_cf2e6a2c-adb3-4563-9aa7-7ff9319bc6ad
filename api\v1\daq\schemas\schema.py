from pydantic import BaseModel, ConfigDict
from datetime import datetime, date
from typing import List, Optional
from api.v1.fishery.device.schema import Ref_DeviceDTO
from fastapi_crud_tortoise import MODEL_ID_TYPE


class SchemaCreateDTO(BaseModel):
    name: str
    schemas: Optional[dict] = None
    description: Optional[str] = None
    status: Optional[bool] = True


class SchemaDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    schemas: Optional[dict] = None
    description: Optional[str] = None
    status: Optional[bool] = True

    model_config = ConfigDict(from_attributes=True)
