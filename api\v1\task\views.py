import json

from fastapi import APIRouter, HTTPException, Query, Depends, Request

# 必须从 redis.asyncio 导入
# from redis import ConnectionPool, Redis
from redis.asyncio import ConnectionPool, Redis

from tortoise.expressions import Q
from api.v1.task.schema import SchedulerTaskRecordDTO, TaskDTO, TaskSimpleOutDTO
from core.redis.dependency import get_redis_pool
from models.task import SchedulerTask, SchedulerTaskRecord

from task.task import TASK_SUBSCRIBE, JobExecStrategy, JobOperation
from fastapi_crud_tortoise import resp_success, resp_fail, pagination_factory, PAGINATION, convert_to_pydantic
from utils.tools import get_uuid_str


router = APIRouter()


async def __redis_call(redis: Redis, message: dict):
    # 生成唯一的回复ID
    reply_id = get_uuid_str()

    # 发送请求
    request = {"message": message, "reply_id": reply_id}

    try:
        await redis.publish(TASK_SUBSCRIBE, json.dumps(request, default=str).encode("utf-8"))

        # 等待服务端返回结果
        response = await redis.blpop(reply_id)
        if response:
            return json.loads(response[1].decode("utf-8"))
    except Exception as e:
        return {"success": False, "msg": str(e)}


async def __add_job(redis: Redis, exec_strategy: JobExecStrategy, job_params: dict) -> int:
    """
    添加任务到消息队列

    使用消息无保留策略：无保留是指当发送者向某个频道发送消息时，如果没有订阅该频道的调用方，就直接将该消息丢弃。

    :param rd: redis 对象
    :param data: 行数据字典
    :return: 接收到消息的订阅者数量。
    """

    message = {
        "operation": JobOperation.ADD,
        "task": {"exec_strategy": exec_strategy, "job_params": job_params},
    }

    return await __redis_call(redis, message)
    # await redis.publish(TASK_SUBSCRIBE, json.dumps(message).encode('utf-8'))


async def __remove_job(redis: Redis, job_id: str):
    message = {"operation": JobOperation.REMOVE, "task": {"job_id": job_id}}

    return await __redis_call(redis, message)
    # await redis.publish(TASK_SUBSCRIBE, json.dumps(message).encode('utf-8'))


async def __remove_all_jobs(redis: Redis):
    message = {"operation": JobOperation.REMOVE_ALL, "task": None}

    return await __redis_call(redis, message)
    # await redis.publish(TASK_SUBSCRIBE, json.dumps(message).encode('utf-8'))


async def __pause_job(redis: Redis, job_id: str):
    message = {"operation": JobOperation.PAUSE, "task": {"job_id": job_id}}

    return await __redis_call(redis, message)
    # await redis.publish(TASK_SUBSCRIBE, json.dumps(message).encode('utf-8'))


async def __resume_job(redis: Redis, job_id: str):
    message = {"operation": JobOperation.RESUME, "task": {"job_id": job_id}}

    return await __redis_call(redis, message)
    # await redis.publish(TASK_SUBSCRIBE, json.dumps(message).encode('utf-8'))


async def __get_job_status(redis: Redis, job_id: str):
    message = {"operation": JobOperation.STATUS, "task": {"job_id": job_id}}

    return await __redis_call(redis, message)


# GET 顺序不能变，'/groups' '/records' '/{_id}' 会冲突
@router.get("/groups", summary="获取定时任务分组选择项列表")
async def get_task_group_options():
    groups = await SchedulerTask.all().distinct().values("group")
    return resp_success(data=[group["group"] for group in groups])


@router.post("/records", summary="获取定时任务调度日志列表")
async def get_task_records(
    # filter: QueryModel,
    filter: SchedulerTaskRecordDTO,
    request: Request,
    pagination: PAGINATION = pagination_factory(),
    sort_by: str = Query(None, description="Sort records by this field"),
):
    skip, limit = pagination.get("skip"), pagination.get("limit")

    filter_dict = filter.model_dump(exclude_none=True)
    query = SchedulerTaskRecord.filter(**filter_dict)
    total = await query.count()

    if skip:
        query = query.offset(skip)

    if limit:
        query = query.limit(limit)

    if sort_by:
        query = query.order_by(sort_by)
    else:
        query = query.order_by("-start_time")

    role_objs = (
        await query
        # .order_by("-start_time")
    )

    current = None
    size = None
    if skip and limit:
        size = limit
        current = skip // limit + 1

    records = convert_to_pydantic(role_objs, SchedulerTaskRecordDTO, mode="json")
    return resp_success(records, total, current, size)


# , response_model=List[TaskSimpleOut]
@router.get("", summary="获取定时任务列表")
async def get_tasks(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    name: str = Query(None, description="name"),
    id: str = Query(None, description="id"),
    group: str = Query(None, description="分组"),
    # v_order:str= Query("desc", description="排序"),
):
    search = Q()

    if name:
        search &= Q(name__icontains=name)
    if id:
        search &= Q(id=id)
    if group:
        search &= Q(group=group)

    query = SchedulerTask.filter(search)
    total = await query.count()
    role_objs = await query.offset((page - 1) * page_size).limit(page_size)

    return resp_success(
        convert_to_pydantic(role_objs, TaskSimpleOutDTO, mode="json"),
        total,
        page,
        page_size,
    )

    # data = {"records": [
    #     TaskSimpleOutDTO.model_validate(role_obj).model_dump() for role_obj in role_objs
    # ]}

    # # .order_by(*order)
    # return SuccessExtra(data=data, total=total, current=page, size=page_size)


@router.post("", summary="添加定时任务")
async def post_tasks(
    request: Request,
    data: TaskDTO,
    redis_pool: ConnectionPool = Depends(get_redis_pool),
):

    task = await SchedulerTask.create(**data.model_dump(exclude_unset=True))
    record = convert_to_pydantic(task, TaskSimpleOutDTO, mode="json")

    exec_strategy = data.exec_strategy
    job_params = {
        "job_id": str(task.id),
        "job_class": data.job_class,
        "expression": data.expression,
    }
    if exec_strategy == JobExecStrategy.interval or exec_strategy == JobExecStrategy.cron:
        job_params["start_date"] = data.start_date
        job_params["end_date"] = data.end_date

    if data.is_active:
        async with Redis(connection_pool=redis_pool) as redis:
            await __add_job(redis, exec_strategy, job_params)

        # scheduler = request.app.state.task
        # ret = scheduler.add_job(exec_strategy, job_params)
        # if ret:
        #     return resp_success(record, msg="任务添加成功(active)")
        # else:
        #     return resp_fail(msg="报错")

    return resp_success(record, msg="任务添加成功(not active)")


@router.put("/{_id}", summary="更新定时任务")
async def put_tasks(
    request: Request,
    _id: str,
    data: TaskDTO,
    redis_pool: ConnectionPool = Depends(get_redis_pool),
):
    task = await SchedulerTask.get(id=_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    await task.update_from_dict(data.model_dump(exclude_unset=True)).save()
    record = convert_to_pydantic(task, TaskSimpleOutDTO, mode="json")

    # scheduler = request.app.state.task
    # ret = scheduler.remove_job(str(_id))

    async with Redis(connection_pool=redis_pool) as redis:
        await __remove_job(redis, _id)

    if task.is_active:
        exec_strategy = task.exec_strategy
        job_params = {
            "job_id": str(task.id),
            "job_class": task.job_class,
            "expression": task.expression,
        }
        if exec_strategy == JobExecStrategy.interval or exec_strategy == JobExecStrategy.cron:
            job_params["start_date"] = task.start_date
            job_params["end_date"] = task.end_date

        async with Redis(connection_pool=redis_pool) as redis:
            ret = await __add_job(redis, exec_strategy, job_params)
            if ret["success"]:
                return resp_success(ret["data"])
            else:
                return resp_fail(msg=ret["msg"])

        # ret = scheduler.add_job(exec_strategy, job_params)

        # if ret:
        #     return resp_success(record, msg="任务更新成功(active)")

        # else:
        #     return resp_fail(msg="任务启动报错")

    return resp_success(record, msg="任务更新成功(not active)")


@router.delete("/{_id}", summary="删除单个定时任务")
async def delete_task(request: Request, _id: str, redis_pool: ConnectionPool = Depends(get_redis_pool)):
    task = await SchedulerTask.get(id=_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    await task.delete()

    # scheduler = request.app.state.task
    # ret = scheduler.remove_job(str(_id))

    async with Redis(connection_pool=redis_pool) as redis:
        await __remove_job(redis, _id)

    record = convert_to_pydantic(task, TaskSimpleOutDTO, mode="json")
    return resp_success(record, msg="任务删除成功")

    # if ret:
    #     record = convert_to_pydantic(task, TaskSimpleOutDTO, mode='json')
    #     return resp_success(record, msg="任务删除成功")
    # else:
    #     return resp_fail(msg="任务删除报错")


@router.delete("", summary="删除所有定时任务")
async def delete_all_tasks(request: Request, redis_pool: ConnectionPool = Depends(get_redis_pool)):
    await SchedulerTask.all().delete()

    async with Redis(connection_pool=redis_pool) as redis:
        await __remove_all_jobs(redis)

    # scheduler = request.app.state.task
    # scheduler.remove_all_jobs()
    return resp_success(msg="删除所有定时任务成功")


@router.get("/{_id}", summary="获取定时任务详情", response_model=TaskSimpleOutDTO)
async def get_task(request: Request, _id: str, redis_pool: ConnectionPool = Depends(get_redis_pool)):
    task = await SchedulerTask.get(id=_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    record = convert_to_pydantic(task, TaskSimpleOutDTO, mode="json")

    async with Redis(connection_pool=redis_pool) as redis:
        status = await __get_job_status(redis, str(task.id))
        record["running"] = status["data"] == "running"

    return resp_success(record)


@router.post("/run_once", summary="执行一次定时任务")
async def run_once_task(request: Request, _id: str, redis_pool: ConnectionPool = Depends(get_redis_pool)):
    task = await SchedulerTask.get(id=_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    # scheduler = request.app.state.task

    exec_strategy = JobExecStrategy.once
    job_params = {"job_id": str(task.id), "job_class": task.job_class}

    async with Redis(connection_pool=redis_pool) as redis:
        ret = await __add_job(redis, exec_strategy, job_params)

    if ret:
        return resp_success(msg="任务已执行")
    else:
        return resp_fail(msg=f"任务执行错误")

    # ret = scheduler.add_job(exec_strategy, job_params)
    # if ret:
    #     return resp_success(msg="任务已执行")
    # else:
    #     return resp_fail(msg="报错")


@router.get("/status/{job_id}", summary="获取定时任务状态")
async def get_task_status(
    request: Request, job_id: str, redis_pool: ConnectionPool = Depends(get_redis_pool)
):
    # scheduler = request.app.state.task
    # status = scheduler.get_job_status(job_id)
    async with Redis(connection_pool=redis_pool) as redis:
        status = await __get_job_status(redis, job_id)

    return resp_success(msg=status)


@router.post("/pause/{job_id}", summary="暂停定时任务")
async def pause_task(request: Request, job_id: str, redis_pool: ConnectionPool = Depends(get_redis_pool)):

    async with Redis(connection_pool=redis_pool) as redis:
        ret = await __pause_job(redis, job_id)

    if ret:
        return resp_success(msg=f"任务 {job_id} 已暂停")
    else:
        return resp_fail(msg=f"任务 {job_id} 未找到")

    # scheduler = request.app.state.task
    # try:
    #     if scheduler.pause(job_id):
    #         return resp_success(msg=f"任务 {job_id} 已暂停")
    #     else:
    #         return resp_fail(msg=f"任务 {job_id} 未找到")
    # except JobLookupError:
    #     raise HTTPException(status_code=404, detail=f"任务 {job_id} 未找到")


@router.post("/resume/{job_id}", summary="恢复定时任务")
async def resume_task(request: Request, job_id: str, redis_pool: ConnectionPool = Depends(get_redis_pool)):

    async with Redis(connection_pool=redis_pool) as redis:
        ret = await __resume_job(redis, job_id)

        if ret:
            return resp_success(msg=f"任务 {job_id} 已恢复")
        else:
            return resp_fail(msg=f"任务 {job_id} 未找到")

    # scheduler = request.app.state.task
    # try:
    #     if scheduler.resume(job_id):
    #         return resp_success(msg=f"任务 {job_id} 已恢复")
    #     else:
    #         return resp_fail(msg=f"任务 {job_id} 未找到")
    # except JobLookupError:
    #     raise HTTPException(status_code=404, detail=f"任务 {job_id} 未找到")
