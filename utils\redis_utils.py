from redis.asyncio import ConnectionPool, Redis
import datetime


def __get_ttl_to_midnight():
    """计算当前时间到当天 0 点的剩余时间（秒）"""
    now = datetime.datetime.now()
    midnight = (now + datetime.timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
    return (midnight - now).seconds


async def merge_and_filter_dict(existing_value: dict, new_value: dict) -> dict:
    """改进后的合并逻辑，处理bytes和str类型键"""
    # 过滤掉值为 None 的键
    filtered_value = {k: v for k, v in new_value.items() if v is not None}
    # 合并新值和原有值
    if existing_value:
        filtered_value = {**existing_value, **filtered_value}
    return filtered_value


async def hset_redis_daily_key(redis: Redis, key: str, value: dict):
    """设置 key 的值，并确保其在每天 0 点过期"""
    ttl = __get_ttl_to_midnight()
    # 获取原有值
    existing_value = await redis.hgetall(key)
    # 合并及覆盖新值和原有值
    merged_value = await merge_and_filter_dict(existing_value, value)
    # 如果 merged_value 不为空，则更新 Redis
    if merged_value:
        await redis.hset(key, mapping=merged_value)
        await redis.expire(key, ttl)  # 设置过期时间


async def hget_redis_daily_key(redis: Redis, key: str, default_value: dict = {}) -> dict:
    """获取并自动转换bytes类型为str"""
    raw_data = await redis.hgetall(key)
    if not raw_data:
        await hset_redis_daily_key(redis, key, default_value)
        return default_value
    return {
        k.decode("utf-8") if isinstance(k, bytes) else k: v.decode("utf-8") if isinstance(v, bytes) else v
        for k, v in raw_data.items()
    }


async def hset_redis_key(redis: Redis, key: str, value: dict):
    """设置 key 的值"""
    # 获取原有值
    existing_value = await redis.hgetall(key)
    # 合并及覆盖新值和原有值
    merged_value = await merge_and_filter_dict(existing_value, value)
    # 如果 merged_value 不为空，则更新 Redis
    if merged_value:
        await redis.hset(key, mapping=merged_value)


async def hget_redis_key(redis: Redis, key: str, default_value: dict = {}) -> dict:
    """通用获取方法"""
    raw_data = await redis.hgetall(key)
    if not raw_data:
        await hset_redis_key(redis, key, default_value)
        return default_value
    return {
        k.decode("utf-8") if isinstance(k, bytes) else k: v.decode("utf-8") if isinstance(v, bytes) else v
        for k, v in raw_data.items()
    }


# 使用场景
# 1. 统计每天的饲喂次数
# 2. 统计每天的饲喂重量
