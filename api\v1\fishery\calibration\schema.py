from pydantic import BaseModel, ConfigD<PERSON>, <PERSON><PERSON>
from datetime import datetime, date
from typing import List, Optional
from api.v1.fishery.device.schema import Ref_DeviceDTO
from api.v1.fishery.organization.schema import Ref_OrganizationDTO
from schemas.users import Ref_UserDTO
from fastapi_crud_tortoise import MODEL_ID_TYPE


class CalLogCreateDTO(BaseModel):
    tenant_id: Optional[MODEL_ID_TYPE] = None
    device_id: MODEL_ID_TYPE
    time: datetime
    feed: str
    model: str
    weight: float


class CalLogDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    tenant_id: Optional[str] = None
    device_id: Optional[MODEL_ID_TYPE] = None
    device: Optional[Ref_DeviceDTO] = None
    time: Optional[datetime] = None
    feed: Optional[str] = None
    model: Optional[str] = None
    weight: Optional[float] = None

    model_config = ConfigDict(from_attributes=True)
