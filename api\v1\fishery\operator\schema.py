from pydantic import BaseModel, ConfigD<PERSON>, <PERSON><PERSON>
from datetime import datetime, date
from typing import List, Optional
from fastapi_crud_tortoise import MODEL_ID_TYPE


class OperatorCreateDTO(BaseModel):
    name: str
    account: str
    password: str
    status: Optional[bool] = True


class OperatorDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    account: Optional[str] = None
    status: Optional[bool] = True
    model_config = ConfigDict(from_attributes=True)


class OperatorPasswordDTO(BaseModel):
    id: MODEL_ID_TYPE
    password: str
