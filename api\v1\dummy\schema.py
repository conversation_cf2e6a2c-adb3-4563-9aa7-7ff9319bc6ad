from pydantic import BaseModel, ConfigDict, <PERSON><PERSON>
from datetime import datetime, date
from typing import List, Optional
from fastapi_crud_tortoise import MODEL_ID_TYPE, FileField


class DummyCreateDTO(BaseModel):
    name: str
    age: int
    salary: float
    is_active: bool
    birthdate: date
    created_at: datetime
    notes: str
    json_data: Optional[object] = None
    file: Optional[FileField] = None

    # class Config:
    #     orm_mode = True
    #     # 此选项将允许我们将ORM对象实例转换为Pydantic对象实例 from_orm


class DummyDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    age: Optional[int] = None
    salary: Optional[float] = None
    is_active: Optional[bool] = None
    birthdate: Optional[date] = None
    created_at: Optional[datetime] = None
    notes: Optional[str] = None
    json_data: Optional[object] = None
    file: Optional[FileField] = None

    # V2
    model_config = ConfigDict(from_attributes=True)

    # V1
    # class Config:
    #     orm_mode = True
    #     from_attributes


###################################################################################


class EmployeeCreateDTO(BaseModel):
    number: str
    name: str
    retire: bool
    retire_date: Optional[datetime] = None
    department_id: Optional[MODEL_ID_TYPE] = None
    # department: Optional[DepartmentModel] = relationship(
    #     "DepartmentModel", backref="employees"
    # )
    # teams: Optional[List[TeamModel]] = relationship(
    #     secondary=teams_employee_table, back_populates="employees"
    # )


class Ref_DepartmentDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    factor: Optional[float] = None

    model_config = ConfigDict(from_attributes=True)


class Ref_TeamDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None


class EmployeeDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    number: Optional[str] = None
    name: Optional[str] = None
    retire: Optional[bool] = None
    retire_date: Optional[datetime] = None
    department: Optional[Ref_DepartmentDTO] = None
    department_id: Optional[MODEL_ID_TYPE] = None
    teams: Optional[List[Ref_TeamDTO]] = None
    teams_refids: Optional[List[MODEL_ID_TYPE]] = None

    model_config = ConfigDict(from_attributes=True)


###################################################################################


class DepartmentCreateDTO(BaseModel):
    name: str
    factor: float


class DepartmentDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    factor: Optional[float] = None
    employees: Optional[List[EmployeeDTO]] = None
    employees_refids: Optional[List[MODEL_ID_TYPE]] = None

    model_config = ConfigDict(from_attributes=True)


###################################################################################


class TeamCreateDTO(BaseModel):
    name: str
    # employees


class TeamDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    employees: Optional[List[EmployeeDTO]] = None
    employees_refids: Optional[List[MODEL_ID_TYPE]] = None

    model_config = ConfigDict(from_attributes=True)


###################################################################################

# teams_employee_table = Table(
#     "association_table",
#     Base.metadata,
#     Column("team_id", ForeignKey("team.id"), primary_key=True),
#     Column("employee_id", ForeignKey("employee.id"), primary_key=True),
# )
