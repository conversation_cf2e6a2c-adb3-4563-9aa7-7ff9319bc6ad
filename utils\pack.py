import abc
import struct
import zlib
import msgpack
import json
from typing import List, Dict, Type

class FloatArrayPacker(abc.ABC):
    @abc.abstractmethod
    def pack(self, float_array: List[float]) -> bytes:
        pass
    
    @abc.abstractmethod
    def unpack(self, packed_data: bytes) -> List[float]:
        pass

class MsgPackPacker(FloatArrayPacker):
    def pack(self, float_array: List[float]) -> bytes:
        return msgpack.packb(float_array, use_single_float=True, use_bin_type=True)
    
    def unpack(self, packed_data: bytes) -> List[float]:
        return msgpack.unpackb(packed_data)

class StructPackerBase(FloatArrayPacker):
    def __init__(self, use_double=False, zip=False):
        self.format_char = 'd' if use_double else 'f'
        self.bytes_per_float = 8 if use_double else 4
        self.zip = zip
    
    def pack(self, float_array: List[float]) -> bytes:
        buf = struct.pack(f'{len(float_array)}{self.format_char}', *float_array)
        if self.zip:
            buf = zlib.compress(buf)
        return buf
    
    def unpack(self, packed_data: bytes) -> List[float]:
        if self.zip:
            packed_data = zlib.decompress(packed_data)
        num_floats = len(packed_data) // self.bytes_per_float
        return list(struct.unpack(f'{num_floats}{self.format_char}', packed_data))

class StructSinglePacker(StructPackerBase):
    def __init__(self):
        super().__init__(use_double=False, zip=False)

class StructSingleZipPacker(StructPackerBase):
    def __init__(self):
        super().__init__(use_double=False, zip=True)

class StructDoublePacker(StructPackerBase):
    def __init__(self):
        super().__init__(use_double=True, zip=False)

class StructDoubleZipPacker(StructPackerBase):
    def __init__(self):
        super().__init__(use_double=True, zip=True)

class JsonPacker(FloatArrayPacker):
    def pack(self, float_array: List[float]) -> bytes:
        return json.dumps(float_array).encode('utf-8')
    
    def unpack(self, packed_data: bytes) -> List[float]:
        return json.loads(packed_data.decode('utf-8'))

class PackerFactory:
    # 注册表存储所有可用的打包器类型
    _packers: Dict[str, Type[FloatArrayPacker]] = {
        "msgpack": MsgPackPacker,
        "json": JsonPacker,
        "struct_single": StructSinglePacker,
        "struct_single_zip": StructSingleZipPacker,
        "struct_double": StructDoublePacker,
        "struct_double_zip": StructDoubleZipPacker
    }
    
    # 默认打包器类型
    DEFAULT_PACKER = "struct_single"
    
    @classmethod
    def register_packer(cls, name: str, packer_class: Type[FloatArrayPacker]):
        """注册新的打包器类型"""
        cls._packers[name] = packer_class
    
    @classmethod
    def get_packer(cls, packer_type: str = None) -> FloatArrayPacker:
        """获取打包器实例，不指定类型时使用默认打包器"""
        packer_name = packer_type or cls.DEFAULT_PACKER
        packer_class = cls._packers.get(packer_name)
        
        if packer_class is None:
            raise ValueError(f"Unknown packer type: {packer_name}. "
                           f"Available packers: {list(cls._packers.keys())}")
        
        return packer_class()



packer = PackerFactory.get_packer()