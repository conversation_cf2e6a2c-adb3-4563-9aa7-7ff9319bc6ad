from pydantic import BaseModel, ConfigD<PERSON>, <PERSON><PERSON>
from datetime import datetime, date
from typing import List, Optional
from api.v1.fishery.device.schema import Ref_DeviceDTO
from schemas.users import Ref_UserDTO
from fastapi_crud_tortoise import MODEL_ID_TYPE


class FaultLogCreateDTO(BaseModel):
    tenant_id: Optional[MODEL_ID_TYPE] = None
    name: str
    device_id: MODEL_ID_TYPE
    version: str
    fault_time: datetime
    resolved_time: Optional[datetime] = None
    status: Optional[bool] = True
    notes: Optional[str] = None


class FaultLogDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    tenant_id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    device_id: Optional[MODEL_ID_TYPE] = None
    device: Optional[Ref_DeviceDTO] = None
    version: Optional[str] = None
    fault_time: Optional[datetime] = None
    resolved_time: Optional[datetime] = None
    status: Optional[bool] = None
    notes: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class FaultLogQueryDTO(BaseModel):
    device_name: Optional[str] = None
    name: Optional[str] = None
    status: Optional[bool] = None
    fault_time_start: Optional[datetime] = None
    fault_time_end: Optional[datetime] = None
    resolved_time_start: Optional[datetime] = None
    resolved_time_end: Optional[datetime] = None
    # fishery: Optional[str] = None
    # workshop: Optional[str] = None
    organization_ids: Optional[List[MODEL_ID_TYPE]] = None
    type: Optional[str] = None
    model: Optional[str] = None
    version: Optional[str] = None


class FaultLogDetailDTO(BaseModel):
    device_id: Optional[MODEL_ID_TYPE] = None
    device_name: Optional[str] = None
    name: Optional[str] = None
    status: Optional[bool] = None
    fault_time: Optional[datetime] = None
    resolved_time: Optional[datetime] = None
    fishery: Optional[str] = None
    workshop: Optional[str] = None
    type: Optional[str] = None
    model: Optional[str] = None
    version: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)
