"""
Descripttion: 
version: 0.x
Author: zhai
Date: 2025-01-16 20:06:15
LastEditors: zhai
LastEditTime: 2025-01-16 23:19:00
"""

from pydantic import BaseModel, ConfigD<PERSON>, <PERSON><PERSON>
from datetime import datetime, date
from typing import List, Optional
from fastapi_crud_tortoise import MODEL_ID_TYPE, FileField


class FirmwareCreateDTO(BaseModel):
    name: str
    version: str
    type: str
    model: str
    file: Optional[FileField] = None
    notes: Optional[str] = None
    model_config = ConfigDict(arbitrary_types_allowed=True)


class Ref_FirmwareDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    version: Optional[str] = None
    type: Optional[str] = None
    model: Optional[str] = None
    file: Optional[FileField] = None
    notes: Optional[str] = None
    status: Optional[bool] = None

    model_config = ConfigDict(from_attributes=True, arbitrary_types_allowed=True)


class FirmwareDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    version: Optional[str] = None
    type: Optional[str] = None
    model: Optional[str] = None
    file: Optional[FileField] = None
    notes: Optional[str] = None
    status: Optional[bool] = None

    model_config = ConfigDict(from_attributes=True, arbitrary_types_allowed=True)
