from datetime import datetime
from tortoise import Model, fields
from fastapi_crud_tortoise import TenantCrudModel


class TagModel(TenantCrudModel):
    """标签"""

    name = fields.CharField(max_length=255)
    notes = fields.CharField(max_length=255, null=True)

    class Meta:
        table = "tags"


class DeviceModel(TenantCrudModel):
    """设备表"""

    sn = fields.CharField(null=False, max_length=255, unique=True, description="序列号")
    name = fields.CharField(null=True, default=None, max_length=255, description="备注名")
    organization = fields.ForeignKeyField(
        "app_system.OrganizationModel", related_name="devices", null=True, description="所属组织"
    )
    tags = fields.ManyToManyField("app_system.TagModel", related_name="devices")  # 多对多关系

    binder = fields.ForeignKeyField(
        "app_system.User", related_name="devices", null=True, description="绑定人"
    )
    type = fields.Char<PERSON><PERSON>(max_length=255, null=True, description="设备类型")
    model = fields.Char<PERSON>ield(max_length=255, null=True, description="设备型号")
    version = fields.Char<PERSON>ield(max_length=255, null=True, blank=True, description="设备版本号")

    params = fields.JSONField(null=True, default=None, description="参数")
    params_time = fields.DatetimeField(null=True, description="设置参数时间")
    params_confirmed = fields.JSONField(null=True, default=None, description="已确认参数")
    params_confirmed_time = fields.DatetimeField(null=True, description="确认时间")

    class Meta:
        table = "device"

    async def save(self, *args, **kwargs):
        """重写 save 方法，设置binder"""
        if self.created_by:
            self.binder_id = self.created_by

        return await super().save(*args, **kwargs)


class FaultLogModel(TenantCrudModel):
    """故障记录表"""

    name = fields.CharField(null=False, max_length=255, description="故障名")
    device = fields.ForeignKeyField(
        "app_system.DeviceModel", related_name="faultlog", null=True, description="设备"
    )
    version = fields.CharField(max_length=255, null=True, blank=True, description="设备版本号")
    fault_time = fields.DatetimeField(null=False, default=datetime.now, description="故障发生时间")
    resolved_time = fields.DatetimeField(null=True, description="故障解决时间")
    status = fields.BooleanField(default=False, description="故障状态，True: 已处理，False: 未处理")
    notes = fields.TextField(null=True, description="备注")

    class Meta:
        table = "fault_log"


class OfflineLogModel(TenantCrudModel):
    """离线记录表"""

    device = fields.ForeignKeyField(
        "app_system.DeviceModel", related_name="offlinelog", null=True, description="设备"
    )
    offline_time = fields.DatetimeField(null=False, default=datetime.now, description="离线时间")
    online_time = fields.DatetimeField(null=True, description="上线时间")
    status = fields.BooleanField(default=False, description="离线状态，True: 离线，False: 在线")
    notes = fields.TextField(null=True, description="备注")

    class Meta:
        table = "offline_log"


class FeedingLogModel(TenantCrudModel):
    """投喂记录表"""

    device = fields.ForeignKeyField(
        "app_system.DeviceModel", related_name="feedinglog", null=True, description="设备"
    )
    time = fields.DatetimeField(null=False, default=datetime.now, description="投喂时间")
    feed_mode = fields.CharField(max_length=255, null=True, description="下料模式")
    feed_type = fields.CharField(max_length=255, null=True, description="饲料类型")
    weight = fields.FloatField(null=True, description="下料重量")
    notes = fields.TextField(null=True, description="备注")

    class Meta:
        table = "feeding_log"


class CalLogModel(TenantCrudModel):
    """校准记录"""

    device = fields.ForeignKeyField(
        "app_system.DeviceModel", related_name="callog", null=True, description="设备"
    )
    time = fields.DatetimeField(null=False, default=datetime.now, description="校准时间")
    feed = fields.CharField(max_length=255, null=True, description="料型")
    model = fields.CharField(max_length=255, null=True, description="料辊")
    weight = fields.FloatField(null=True, description="下料量")

    class Meta:
        table = "cal_log"


__all__ = [
    "DeviceModel",
    "FaultLogModel",
    "OfflineLogModel",
    "FeedingLogModel",
    "TagModel",
    "CalLogModel",
]
