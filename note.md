poetry run python -m PyInstaller -F -i main.ico --copy-metadata tortoise-orm --hidden-import fastapi_cache --hidden-import passlib.handlers.argon2 --hidden-import tortoise.backends.sqlite --hidden-import aerich.ddl.postgres --hidden-import aerich.ddl.sqlite --hidden-import tortoise.backends.mysql --hidden-import aerich.ddl.mysql --hidden-import cryptography --hidden-import fastapi_crud_tortoise --hidden-import tortoise.backends.asyncpg --exclude-module pyinstaller --add-data="static/docs:static/docs" run.py