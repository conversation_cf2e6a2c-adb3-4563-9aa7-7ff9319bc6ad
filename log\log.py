import re
import sys
import time
import logging
from types import FrameType
from typing import cast
from loguru import logger

from core.ctx import CTX_X_REQUEST_ID
from settings import APP_SETTINGS
from uvicorn.config import LOGGING_CONFIG



# 移除 ANSI，添加x_request_id
ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
def remove_ansi_add_request_id(record):
    record["message"] = ansi_escape.sub('', record["message"])
    record["x_request_id"] = CTX_X_REQUEST_ID.get()
    return True

LOG_FMT = "%(asctime)s - %(levelprefix)s %(message)s"

class Logger:
    """输出日志到文件和控制台"""

    def __init__(self):
        log_name = f"Fast_{time.strftime('%Y-%m-%d', time.localtime()).replace('-', '_')}.log"
        log_path = APP_SETTINGS.LOGS_ROOT / log_name
        self.logger = logger
        self.logger.remove()
        APP_SETTINGS.LOGS_ROOT.mkdir(parents=True, exist_ok=True)
        self.logger.add(sys.stdout)

        self.logger.add(
            log_path,
            format="{time:YYYY-MM-DD HH:mm:ss} - "
            "{process.name} | "
            "{thread.name} | "
            "<red> {x_request_id} </red> | "
            "{module}.{function}:{line} - {level} -{message}",
            encoding="utf-8",
            retention="3 days",
            backtrace=True,
            diagnose=True,
            enqueue=True,
            rotation="00:00",
            filter=remove_ansi_add_request_id,
        )

        self.init_config()

    @staticmethod
    def init_config():
        # 设置uvicorn日志格式
        LOGGING_CONFIG["formatters"]["default"]["fmt"] = LOG_FMT
        LOGGING_CONFIG["formatters"]["access"]["fmt"] = LOG_FMT

        LOGGER_NAMES = ("uvicorn.asgi", "uvicorn.access", "uvicorn")
        logging.getLogger().handlers = [InterceptHandler()]
        for logger_name in LOGGER_NAMES:
            logging_logger = logging.getLogger(logger_name)
            logging_logger.handlers = [InterceptHandler()]

    def get_logger(self):
        return self.logger


class InterceptHandler(logging.Handler):
    def __init__(self):
        super().__init__()
        self.setFormatter(logging.Formatter(LOG_FMT))

    def emit(self, record: logging.LogRecord) -> None:
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = str(record.levelno)

        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = cast(FrameType, frame.f_back)
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())


Loggers = Logger()
log = Loggers.get_logger()
