import json
from typing import List, cast
from fastapi import Depends, Request, HTTPException, Query
from datetime import datetime

from api.v1.utils import insert_log
from controllers.device import DeviceController
from models.fishery.customer import CustomerModel, OperatorModel
from models.system.utils import LogDetailType, LogType
from schemas.users import UserCreate, UserUpdate
from utils.security import get_password_hash
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    UserDataOption,
    TenantDataOption,
    insert_operation,
    OperationType,
    PAGINATION,
    apply_user_tenant_filters,
)

from utils.tools import get_uuid_str_without_hyphen
from .schema import (
    DeviceCreateDTO,
    DeviceDTO,
    DeviceDetailDTO,
    DeviceDetailFilterDTO,
    DeviceParamsDTO,
    DeviceStatus,
    DeviceDetailFeed,
    DeviceUpdateParamsDTO,
    HandshakeDTO,
    RebindD<PERSON>,
    <PERSON><PERSON>UpdateD<PERSON>,
)
from models.fishery import DeviceModel
from controllers.user import user_controller
from redis.asyncio import ConnectionPool, Redis
from core.redis.dependency import get_redis_pool
from utils.remote_utils import remote_call_gateway, remote_mqtt_gateway
from tortoise.expressions import Q
from settings import APP_SETTINGS
from tortoise.functions import Sum, Function, Count
from tortoise.expressions import F, RawSQL, Subquery


router = TortoiseCRUDRouter(
    schema=DeviceDTO,
    create_schema=DeviceCreateDTO,
    update_schema=DeviceUpdateDTO,
    db_model=DeviceModel,
    prefix="device",
    tags=["设备管理"],
    kbatch_create_route=False,
    kupsert_route=False,
    user_data_option=UserDataOption.SELF_DEFAULT,
    tenant_data_option=TenantDataOption.TENANT_DEFAULT,
)


def remove_dashes_from_guid(guid):
    """
    Remove dashes from a GUID string.

    Args:
        guid (str): A GUID string with dashes.

    Returns:
        str: A GUID string without dashes.
    """
    return guid.replace("-", "")


@router.post("/handshake", response_model=RespModelT[bool], summary="绑定前握手")
async def handshake(
    data: HandshakeDTO,
    request: Request,
    redis_pool: ConnectionPool = Depends(get_redis_pool),
):
    """绑定前握手"""
    # 查询设备
    device = await DeviceModel.get_or_none(sn=data.sn)
    if device:
        return resp_fail(msg="该设备已被绑定")

    topic = f"{APP_SETTINGS.MQTT_TOPIC_HEADER}/cmd/{data.sn}"
    reply_id = get_uuid_str_without_hyphen()

    tenant_id = getattr(request.state, "tenant_id", None)
    if tenant_id is None:
        return resp_fail(msg="非租户账户")

    customer_id = remove_dashes_from_guid(tenant_id)
    payload_dict = {
        "id": reply_id,
        "type": "bind",
        "data": {
            "customerId": customer_id,
        },
    }

    async with Redis(connection_pool=redis_pool) as redis:
        ret = await remote_mqtt_gateway(redis, topic, payload_dict, reply_id, data.timeout or 10)
        if ret["success"]:
            # 记录操作日志
            await insert_operation(
                user=getattr(request.state, "user_name", "unknown"),
                action=OperationType.BIND,
                target=router.target,
                notes=json.dumps(data, default=str, indent=1),
                tenant_id=tenant_id,
                user_id=getattr(request.state, "user_id", None),
                trace_id=getattr(request.state, "x_request_id", None),
            )

            return resp_success(data=True)
        else:
            return resp_fail(msg=ret["msg"])


@router.post("/rebind", response_model=RespModelT[list[DeviceDTO]], summary="重新绑定")
async def rebind(request: Request, data: RebindDTO):
    # 查询所有设备
    devices = await DeviceModel.filter(id__in=data.device_ids).all()
    if not devices:
        return resp_fail(msg="设备不存在")

    user = await user_controller.get_by_username(data.user_account)
    if not user:
        return resp_fail(msg="用户不存在")

    cur_user_id = getattr(request.state, "user_id", None)

    # 批量更新设备的created_by字段
    for device in devices:
        device.created_by = user.id
        device.updated_by = cur_user_id
        device.binder_id = user.id

    await DeviceModel.bulk_update(devices, fields=["created_by", "updated_by", "binder_id"])

    for device in devices:
        await device.fetch_related(*DeviceModel._meta.fetch_fields)

    # 记录操作日志
    await insert_operation(
        user=getattr(request.state, "user_name", "unknown"),
        action=OperationType.REBIND,
        target=router.target,
        notes=json.dumps(data, default=str, indent=1),
        tenant_id=getattr(request.state, "tenant_id", None),
        user_id=cur_user_id,
        trace_id=getattr(request.state, "x_request_id", None),
    )

    return resp_success(convert_to_pydantic(devices, DeviceDTO, relationships=True))


@router.post("/update_params", response_model=RespModelT[DeviceDTO], summary="更新设备参数")
async def update_device_params(
    request: Request,
    data: DeviceUpdateParamsDTO,
    redis_pool: ConnectionPool = Depends(get_redis_pool),
):
    """更新设备参数"""
    try:
        # 查询设备
        devices = await DeviceModel.filter(id__in=data.device_ids).all()
        if not devices:
            return resp_fail(msg="设备不存在")

        for device in devices:
            # 更新 params 和 params_time
            device.params = data.params.model_dump()
            device.params_time = datetime.now()

            # 保存设备
            await device.save()

        # 记录操作日志
        # await insert_operation(
        #     user=getattr(request.state, "user_name", "unknown"),
        #     action=OperationType.SEND,
        #     target=router.target,
        #     notes=json.dumps(data, default=str, indent=1),
        #     tenant_id=getattr(request.state, "tenant_id", None),
        #     user_id=getattr(request.state, "user_id", None),
        #     trace_id=getattr(request.state, "x_request_id", None),
        # )

        if data.operation:
            await insert_operation(
                user=getattr(request.state, "user_name", "unknown"),
                action=data.operation.action,
                target=data.operation.target,
                notes=data.operation.notes,
                tenant_id=getattr(request.state, "tenant_id", None),
                user_id=getattr(request.state, "user_id", None),
                trace_id=getattr(request.state, "x_request_id", None),
            )

        # 通讯
        # topic = "rpc/xxx"
        # timeout = 10
        # async with Redis(connection_pool=redis_pool) as redis:
        #     ret = await remote_call_gateway(redis, topic, data.params, timeout)
        #     if ret["success"]:
        #         return resp_success(ret["data"])
        #     else:
        #         return resp_fail(msg=ret["msg"])

        # 返回更新后的设备信息
        return resp_success(convert_to_pydantic(devices, DeviceDTO))

    except Exception as e:
        return resp_fail(msg=f"更新设备参数失败: {str(e)}")


# class DeviceDetailFilterDTO(BaseModel):
#     customer_name: Optional[str] = Field(default=None, description="客户名")
#     binder_name: Optional[str] = Field(default=None, description="绑定人")
#     name_sn: Optional[str] = Field(default=None, description="名称或SN")
#     organization_ids: Optional[list[MODEL_ID_TYPE]] = Field(default=None, description="组织ID")
#     tags: Optional[list[str]] = Field(default=None, description="标签")
#     type: Optional[str] = Field(default=None, description="类型")
#     model: Optional[str] = Field(default=None, description="型号")


@router.post("/query_detail", response_model=RespModelT[List[DeviceDetailDTO]], summary="查询设备详情")
async def query_detail(
    filter: DeviceDetailFilterDTO,
    request: Request,
    redis_pool: ConnectionPool = Depends(get_redis_pool),
    pagination: PAGINATION = router.pagination,
    sort_by: str = Query(None, description="Sort records by this field"),
    relationships: bool = False,
    user_data_filter: router.user_data_filter_type = router.user_data_filter_defv,
    tenant_data_filter: router.tenant_data_filter_type = router.tenant_data_filter_defv,
):
    """查询设备详情"""

    # 初始化 customer_ids 和 binder_ids
    tenant_ids = None
    binder_ids = []

    if filter.customer_name:
        test = await CustomerModel.filter(name__icontains=filter.customer_name).prefetch_related("tenant")
        tenant_ids = (
            await CustomerModel.filter(name__icontains=filter.customer_name)
            .prefetch_related("tenant")
            .values_list("tenant_id", flat=True)
        )

    if filter.binder_name:
        binder_ids = await OperatorModel.filter(name__icontains=filter.binder_name).values_list(
            "id", flat=True
        )

    skip, limit = pagination.get("skip"), pagination.get("limit")

    try:
        sql_query = router.db_model.filter(enabled_flag=True)
        sql_query = apply_user_tenant_filters(sql_query, request, user_data_filter, tenant_data_filter)

        if filter.id:
            sql_query = sql_query.filter(**{router._pk: filter.id})
        if tenant_ids is not None:
            sql_query = sql_query.filter(tenant_id__in=tenant_ids)
        if binder_ids:
            sql_query = sql_query.filter(binder_id__in=binder_ids)
        if filter.name_sn:
            sql_query = sql_query.filter(Q(name__icontains=filter.name_sn) | Q(sn__icontains=filter.name_sn))
        if filter.organization_ids:
            sql_query = sql_query.filter(organization_id__in=filter.organization_ids)
        if filter.tags:
            sql_query = sql_query.filter(tags__name__in=filter.tags).distinct()
            sql_query = sql_query.prefetch_related("tags")
        if filter.type:
            sql_query = sql_query.filter(type=filter.type)
        if filter.model:
            sql_query = sql_query.filter(model=filter.model)

        # 这种方式统计的数量是device_tags表的数量
        # total = await sql_query.count()

        # 通过聚合函数显式去重
        total_query = sql_query.annotate(total=RawSQL("COUNT(DISTINCT (device.id))")).values("total")
        total_result = await total_query
        total = total_result[0]["total"] if total_result else 0

        if sort_by:
            sql_query = sql_query.order_by(sort_by)

        if skip:
            sql_query = sql_query.offset(cast(int, skip))

        if limit:
            sql_query = sql_query.limit(limit)

        if relationships:
            sql_query = router._autoload_options(sql_query)

        # 关联organization和parent
        # 受relationships影响
        # sql_query = sql_query.select_related("organization", "organization__parent")

        objs = await sql_query

        current = None
        size = None
        if skip and limit:
            size = limit
            current = skip // limit + 1

        async with Redis(connection_pool=redis_pool) as redis:
            device_controller = DeviceController(redis)
            for obj in objs:
                obj.status = await device_controller.get_device_status_by_id(
                    obj.id,
                    DeviceStatus(
                        online_status="False",
                        last_online_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        fault_status=0,
                        feed_status=0,
                        signal_strength=100,
                    ),
                )
                obj.daily_feed = await device_controller.get_device_feed_by_id(
                    obj.id,
                    DeviceDetailFeed(
                        task_count=10,
                        finish_count=5,
                        weight=100,
                        todayWeight=100,
                    ),
                )

        # 转换为DTO
        device_dtos = []
        for obj in objs:
            dto = convert_to_pydantic(obj, DeviceDetailDTO, relationships)

            # 检查 obj.organization 是否是 QuerySet，如果是则执行查询
            if obj.organization:
                organization = (
                    await obj.organization if hasattr(obj.organization, "all") else obj.organization
                )
                dto["workshop"] = organization.name

                # 检查 organization.parent 是否是 QuerySet，如果是则执行查询
                if organization.parent:
                    parent = (
                        await organization.parent
                        if hasattr(organization.parent, "all")
                        else organization.parent
                    )
                    dto["fishery"] = parent.name

            device_dtos.append(dto)

        return resp_success(
            device_dtos,
            total,
            current,
            size,
        )
    except Exception as e:
        return resp_fail(msg=f"查询设备详情失败: {str(e)}")


# @router.post("/query_detail", response_model=RespModelT[DeviceDetailDTO], summary="查询设备详情")
# async def query_detail(
#     filter: router.filter_schema,  # type: ignore
#     request: Request,
#     redis_pool: ConnectionPool = Depends(get_redis_pool),
#     pagination: PAGINATION = router.pagination,
#     sort_by: str = Query(None, description="Sort records by this field"),
#     relationships: bool = False,
#     user_data_filter: router.user_data_filter_type = router.user_data_filter_defv,
#     tenant_data_filter: router.tenant_data_filter_type = router.tenant_data_filter_defv,
# ):
#     """查询设备详情"""

#     objs, total, current, size = await router.fun_query(
#         filter, request, pagination, sort_by, relationships, user_data_filter, tenant_data_filter
#     )

#     async with Redis(connection_pool=redis_pool) as redis:
#         device_controller = DeviceController(redis)
#         for obj in objs:
#             obj.status = await device_controller.get_device_status_by_id(
#                 obj.id,
#                 DeviceStatus(
#                     online_status="True",
#                     last_online_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
#                     fault_status=0,
#                     feed_status=0,
#                     signal_strength=100,
#                 ),
#             )
#             obj.daily_feed = await device_controller.get_device_feed_by_id(
#                 obj.id,
#                 DeviceDetailFeed(
#                     task_count=10,
#                     finish_count=5,
#                     weight=100,
#                     todayWeight=100,
#                 ),
#             )

#     return resp_success(
#         convert_to_pydantic(objs, DeviceDetailDTO, relationships),
#         total,
#         current,
#         size,
#     )


# params =
# {
#     "task": {
#         "mode": "meal",
#         "weight": 6660,
#         "meal": [
#             {"time": "01:00:00", "ratio": 10, "weight": 10, "days": ["Mon", "Tue"]},
#             {"time": "01:00:00", "ratio": 10, "weight": 10, "days": ["Mon", "Tue"]},
#             {"time": "01:00:00", "ratio": 10, "weight": 10, "days": ["Mon", "Tue"]},
#         ],
#         "gap": [
#             {
#                 "timeRange": ["10:10:00", "10:10:00"],
#                 "ratio": 20,
#                 "weight": 200,
#                 "duration": 20,
#                 "rest": 20,
#                 "days": ["Mon", "Tue"],
#             },
#             {
#                 "timeRange": ["10:10:00", "10:10:00"],
#                 "ratio": 20,
#                 "weight": 200,
#                 "duration": 20,
#                 "rest": 20,
#                 "days": ["Mon", "Tue"],
#             },
#         ],
#         "manual": 10,
#     },
#     "system": {"gear": 1, "speed": 80, "model": "a", "feed": "200g"},
#     "guide": {"time": 20, "gear": 1, "speed": 80},
# }

# status = {
#     "last_online_time": "2025-02-11 10:10:10",
#     "online_status": "True",
#     "fault_status": "True",
#     "feed_status": "True",
#     "signal_strength": 0,
# }
# daily_feed = {
#     "task_count": 0,
#     "finish_count": 0,
#     "weight": 0,
#     "todayWeight": 0,
# }
