from pydantic import <PERSON>Model, Config<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>
from datetime import datetime, date
from typing import List, Optional
from api.v1.fishery.device.schema import Ref_DeviceDTO
from schemas.users import Ref_UserDTO
from fastapi_crud_tortoise import MODEL_ID_TYPE


class FeedingLogCreateDTO(BaseModel):
    tenant_id: Optional[MODEL_ID_TYPE] = None
    device_id: MODEL_ID_TYPE
    time: datetime
    feed_mode: str
    feed_type: str
    weight: float
    notes: Optional[str] = None


class FeedingLogDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    tenant_id: Optional[MODEL_ID_TYPE] = None
    device_id: Optional[MODEL_ID_TYPE] = None
    device: Optional[Ref_DeviceDTO] = None
    time: Optional[datetime] = None
    feed_mode: Optional[str] = None
    feed_type: Optional[str] = None
    weight: Optional[float] = None
    notes: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class FeedingLogQueryDTO(BaseModel):
    # fishery: Optional[str] = None
    # workshop: Optional[str] = None
    organization_ids: Optional[List[MODEL_ID_TYPE]] = None
    device_name: Optional[str] = None
    device_model: Optional[str] = None
    tags: Optional[List[str]] = None
    time_start: Optional[datetime] = None
    time_end: Optional[datetime] = None
    feed_mode: Optional[str] = None
    feed_type: Optional[str] = None


class FeedingLogDetailDTO(BaseModel):
    device_id: Optional[MODEL_ID_TYPE] = None
    fishery: Optional[str] = None
    workshop: Optional[str] = None
    device_name: Optional[str] = None
    device_model: Optional[str] = None
    tags: Optional[List[str]] = None
    time: Optional[datetime] = None
    feed_mode: Optional[str] = None
    feed_type: Optional[str] = None
    weight: Optional[float] = None
    notes: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class FeedingStatQueryDTO(BaseModel):
    organization_ids: Optional[List[MODEL_ID_TYPE]] = None
    device_id: Optional[MODEL_ID_TYPE] = None
    time_start: Optional[datetime] = None
    time_end: Optional[datetime] = None


class FeedingStatTrendDTO(BaseModel):
    fishery: Optional[str] = None
    workshop: Optional[str] = None
    day: date
    weight: float


class FeedingStatTypeTrendDTO(BaseModel):
    day: date
    fishery: Optional[str] = None
    workshop: Optional[str] = None
    feed_type: Optional[str] = None
    weight: float


class FeedingTypeDTO(BaseModel):
    type: Optional[str]
    weight: float


class FeedingStatTypeDTO(BaseModel):
    fishery: Optional[str] = None
    workshop: Optional[str] = None
    feeding: List[FeedingTypeDTO]
