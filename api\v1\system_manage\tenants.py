from pydantic import BaseModel, ConfigDict, <PERSON><PERSON>
from datetime import datetime, date
from typing import List, Optional
from fastapi_crud_tortoise import MODEL_ID_TYPE
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    TenantModel,
    resp_success,
    UserDataOption,
    TenantDataOption,
)


class TenantCreateDTO(BaseModel):
    name: str
    description: Optional[str] = None


class TenantDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    description: Optional[str] = None

    # V2
    model_config = ConfigDict(from_attributes=True)


router = TortoiseCRUDRouter(
    schema=TenantDTO,
    create_schema=TenantCreateDTO,
    db_model=TenantModel,
    prefix="tenant",
    tags=["租户管理"],
    user_data_option=UserDataOption.ALL_ONLY,
    tenant_data_option=TenantDataOption.ALL_ONLY,
)
