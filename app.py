"""
Descripttion:
version: 0.x
Author: zhai
Date: 2025-01-04 15:11:53
LastEditors: zhai
LastEditTime: 2025-01-05 11:46:16
"""

import os
from contextlib import asynccontextmanager
from datetime import datetime

from fastapi import FastAPI, applications
from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend
from redis import asyncio as aioredis
from starlette.staticfiles import StaticFiles

from api.v1.utils import refresh_api_list
from core.exceptions import SettingNotFound
from core.init_app import (
    init_menus,
    init_users,
    make_middlewares,
    modify_db,
    register_db,
    register_exceptions,
    register_routers,
)
from core.redis.lifespan import init_redis, shutdown_redis
from log import log
from models.system import Log
from models.system import LogType, LogDetailType
from utils.tools import get_resource_path
from fastapi.openapi.docs import get_swagger_ui_html


try:
    from settings import APP_SETTINGS, REDIS_URL
except ImportError:
    raise SettingNotFound("Can not import settings")


class FastApiEx(FastAPI):

    # 这里是FastApiEx类的初始化方法
    # 我们重写了FastAPI的初始化方法，以支持自定义Swagger UI的静态文件路径
    # swagger_static_path: Swagger UI静态文件的本地路径
    # swagger_static_url: Swagger UI静态文件的URL路径
    # 这样可以在打包成可执行文件时，确保Swagger UI的资源文件能够正确加载
    def __init__(self, *args, **kwargs) -> None:

        if "swagger_static_path" in kwargs:
            self.swagger_static_path = kwargs.pop("swagger_static_path")
            self.swagger_static_url = kwargs.pop("swagger_static_url")

            self.swagger_js_url = f"{self.swagger_static_url}/swagger-ui-bundle.js"
            self.swagger_css_url = f"{self.swagger_static_url}/swagger-ui.css"
            self.swagger_favicon_url = f"{self.swagger_static_url}/favicon.png"

        def get_swagger_ui_html_with_localfile(*args, **kwargs):
            return get_swagger_ui_html(
                *args,
                **kwargs,
                swagger_js_url=self.swagger_js_url,
                swagger_css_url=self.swagger_css_url,
                swagger_favicon_url=self.swagger_favicon_url,
            )

        applications.get_swagger_ui_html = get_swagger_ui_html_with_localfile
        super(FastApiEx, self).__init__(*args, **kwargs)

        swagger_static_path_run = get_resource_path(self.swagger_static_path)

        swagger_abspath = os.path.abspath(swagger_static_path_run)
        print(f"Swagger Resource Path: '{self.swagger_static_path}' Absolute Path: {swagger_abspath}")

        if not os.path.exists(swagger_abspath):
            raise FileNotFoundError(f"Swagger static path does not exist: {self.swagger_static_path}")

        self.mount(self.swagger_static_url, StaticFiles(directory=swagger_static_path_run), name="static")


def create_app() -> FastAPI:
    if APP_SETTINGS.DEBUG:
        _app = FastApiEx(
            title=APP_SETTINGS.APP_TITLE,
            swagger_static_path="./static/docs",
            swagger_static_url="/swagger_static",
            description=APP_SETTINGS.APP_DESCRIPTION,
            version=APP_SETTINGS.VERSION,
            openapi_url="/openapi.json",
            middleware=make_middlewares(),
            lifespan=lifespan,
        )
    else:
        _app = FastApiEx(
            title=APP_SETTINGS.APP_TITLE,
            swagger_static_path="./static/docs",
            swagger_static_url="/swagger_static",
            description=APP_SETTINGS.APP_DESCRIPTION,
            version=APP_SETTINGS.VERSION,
            openapi_url=None,
            middleware=make_middlewares(),
            lifespan=lifespan,
        )
    register_exceptions(_app)
    register_routers(_app, prefix="/api")

    redis = aioredis.from_url(url=REDIS_URL)
    FastAPICache.init(RedisBackend(redis), prefix="fastapi-cache")
    return _app


@asynccontextmanager
async def lifespan(_app: FastAPI):
    start_time = datetime.now()
    try:
        await init_redis(_app)
        await modify_db()

        register_db(_app)

        await init_menus()
        await init_users()
        await Log.create(log_type=LogType.SystemLog, log_detail_type=LogDetailType.SystemStart)
        yield

    except Exception as e:
        print(f"lifespan error: {e}")

    finally:
        await shutdown_redis(_app)

        end_time = datetime.now()
        runtime = (end_time - start_time).total_seconds() / 60
        log.info(f"App {_app.title} runtime: {runtime} min")  # noqa
        await Log.create(log_type=LogType.SystemLog, log_detail_type=LogDetailType.SystemStop)


app = create_app()

app.mount("/static", StaticFiles(directory=APP_SETTINGS.STATIC_ROOT), name="static")
