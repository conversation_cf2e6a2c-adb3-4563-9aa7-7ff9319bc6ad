from pydantic import BaseModel, Before<PERSON><PERSON><PERSON><PERSON>, ConfigDict, <PERSON><PERSON>
from datetime import datetime, date
from typing import Annotated, List, Literal, Optional
from api.v1.system_manage.tenants import TenantDTO
from fastapi_crud_tortoise import MODEL_ID_TYPE


# 定义 bytes 转换器（使用 BeforeValidator）
def convert_bytes(v: any) -> str:
    if isinstance(v, bytes):
        return v.decode("utf-8")
    return v


# PAYLOAD_TYPE = Annotated[str, BeforeValidator(convert_bytes)]
PAYLOAD_TYPE = str


class MqttMessageCreateDTO(BaseModel):
    session_id: Optional[str] = None
    sender: Literal["cloud", "device"]
    type: Optional[str] = None
    system: Optional[bool] = None
    operator: Optional[str] = None
    topic: str
    payload: PAYLOAD_TYPE
    status: Optional[Literal["none", "pending", "replied", "timeout"]] = None


class MqttMessageDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    session_id: Optional[str] = None
    sender: Optional[Literal["cloud", "device"]] = None
    type: Optional[str] = None
    system: Optional[bool] = None
    operator: Optional[str] = None
    topic: Optional[str] = None
    payload: Optional[PAYLOAD_TYPE] = None
    status: Optional[Literal["none", "pending", "replied", "timeout"]] = None

    model_config = ConfigDict(from_attributes=True)


class MqttMessageFilterDTO(BaseModel):
    tenant_id: Optional[MODEL_ID_TYPE] = None
    topic: Optional[str] = None
    type: Optional[str] = None
    system: Optional[bool] = None
    operator: Optional[str] = None
    sn: Optional[str] = None
    status: Optional[Literal["none", "pending", "replied", "timeout"]] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


class MqttReplyDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    sender: Optional[Literal["cloud", "device"]] = None
    type: Optional[str] = None
    topic: Optional[str] = None
    payload: Optional[PAYLOAD_TYPE] = None
    created_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class MqttMessageDetailDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    tenant_id: Optional[MODEL_ID_TYPE] = None
    customer: Optional[str] = None
    fishery: Optional[str] = None
    workshop: Optional[str] = None
    device_name: Optional[str] = None
    device_type: Optional[str] = None
    device_model: Optional[str] = None
    device_tags: Optional[List[str]] = None
    sender: Optional[Literal["cloud", "device"]] = None
    type: Optional[str] = None
    system: Optional[bool] = None
    operator: Optional[str] = None
    topic: Optional[str] = None
    payload: Optional[PAYLOAD_TYPE] = None
    status: Optional[Literal["none", "pending", "replied", "timeout"]] = None
    created_at: Optional[datetime] = None
    replies: Optional[List[MqttReplyDTO]] = None

    model_config = ConfigDict(from_attributes=True)
